import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Question } from './question.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';

@Entity('question_medical_condition')
export class QuestionMedicalCondition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Question, (question) => question.questionMedicalConditions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'question_id' })
  question: Question;

  @ManyToOne(
    () => MedicalCondition,
    (medicalCondition) => medicalCondition.questionMedicalConditions,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'medical_condition_id' })
  medical_condition: MedicalCondition;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;
}
