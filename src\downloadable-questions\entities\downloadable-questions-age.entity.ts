import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { DownloadableQuestion } from './downloadable-question.entity';

@Entity('downloadable_questions_age')
export class DownloadableQuestionsAge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int' })
  age: number;

  @Column({ name: 'downloadable_questions_id', type: 'varchar' })
  downloadable_questions_id: string;

  @ManyToOne(() => DownloadableQuestion, (question) => question.questionAges, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'downloadable_questions_id' })
  downloadableQuestion: DownloadableQuestion;
}
