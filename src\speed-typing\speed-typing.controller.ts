import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { SpeedTypingService } from './speed-typing.service';
import { CreateSpeedTypingDto } from './dto/create-speed-typing.dto';
import { UpdateSpeedTypingDto } from './dto/update-speed-typing.dto';

import { FindManyOptions, Like } from 'typeorm';
import { ExerciseTypesService } from 'src/exercise-types/exercise-types.service';
import { ResponseService } from 'src/common/services/response.service';

@Controller('speed-typing')
export class SpeedTypingController {
  constructor(
    private readonly speedTypingService: SpeedTypingService,
    private readonly responseService: ResponseService,
    private readonly exerciseTypeService: ExerciseTypesService,
  ) {}

  @Post()
  async create(@Body() createSpeedTypingDto: CreateSpeedTypingDto) {
    const question_type = await this.exerciseTypeService.findOne(
      createSpeedTypingDto.exercise_type_id,
    );
    if (question_type.exercise !== 'speed_typing') {
      throw new BadRequestException('Invalid Exercise Type');
    }
    const savedSpeedTyping =
      await this.speedTypingService.create(createSpeedTypingDto);

    const ages = await this.speedTypingService.addAgeToSpeedTyping(
      savedSpeedTyping,
      createSpeedTypingDto.ages,
    );
    return this.responseService.successResponse(
      'New SpeedTyping Question Created Successfully',
      savedSpeedTyping,
    );
  }

  @Get()
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [downloadableQuestions, total] =
      await this.speedTypingService.findAndCountByParam(where, skip, limit, [
        'exerciseType',
      ]);

    return this.responseService.successResponse('SpeedTyping List', {
      data: downloadableQuestions,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get('users')
  async findAllUsers(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [downloadableQuestions, total] =
      await this.speedTypingService.findAndCountByParam(where, skip, limit, [
        'exerciseType',
      ]);

    return this.responseService.successResponse('SpeedTyping List', {
      data: downloadableQuestions,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.speedTypingService.findOne(id);
    return this.responseService.successResponse(
      'SpeedTyping Question Details',
      data,
    );
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateSpeedTypingDto: UpdateSpeedTypingDto,
  ) {
    const savedHearAndSelect = await this.speedTypingService.update(
      id,
      updateSpeedTypingDto,
    );
    if (updateSpeedTypingDto.ages && updateSpeedTypingDto.ages.length) {
      savedHearAndSelect.ages =
        await this.speedTypingService.addAgeToSpeedTyping(
          savedHearAndSelect,
          updateSpeedTypingDto.ages,
        );
    }
    return this.responseService.successResponse(
      'SpeedTyping updated Successfully',
    );
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.speedTypingService.remove(id);
    return this.responseService.successResponse(
      'SpeedTyping Question deleted successfully',
    );
  }

  @Post(':id/ages')
  async addAges(@Param('id') id: string, @Body('ages') ages: number[]) {
    const speedTyping = await this.speedTypingService.findOne(id);
    const addedAges = await this.speedTypingService.addAgeToSpeedTyping(
      speedTyping,
      ages,
    );
    return this.responseService.successResponse(
      'Ages added to SpeedTyping successfully',
      addedAges,
    );
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.question_type_id) {
      where['question_type_id'] = filter.question_type_id;
    }
    if (filter.exercise_type_id) {
      where['exercise_type_id'] = filter.exercise_type_id;
    }
    return where;
  }
}
