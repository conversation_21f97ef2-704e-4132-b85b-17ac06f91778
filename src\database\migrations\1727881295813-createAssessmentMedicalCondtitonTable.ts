import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateAssessmentMedicalCondtitonTable1727881295813
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'assessment_medical_condition',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'assessment_id',
            type: 'varchar',
          },
          {
            name: 'medical_condition_id',
            type: 'varchar',
          },
        ],
      }),
    );

    await queryRunner.createForeignKey(
      'assessment_medical_condition',
      new TableForeignKey({
        columnNames: ['assessment_id'],
        referencedTableName: 'assessments',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'assessment_medical_condition',
      new TableForeignKey({
        columnNames: ['medical_condition_id'],
        referencedTableName: 'medical_condition',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('assessment_medical_condition');
    const assessmentForeignKey = table.foreignKeys.find((fk) =>
      fk.columnNames.includes('assessment_id'),
    );
    const medicalConditionForeignKey = table.foreignKeys.find((fk) =>
      fk.columnNames.includes('medical_condition_id'),
    );
    if (assessmentForeignKey) {
      await queryRunner.dropForeignKey(
        'assessment_medical_condition',
        assessmentForeignKey,
      );
    }
    if (medicalConditionForeignKey) {
      await queryRunner.dropForeignKey(
        'assessment_medical_condition',
        medicalConditionForeignKey,
      );
    }
    await queryRunner.dropTable('assessment_medical_condition');
  }
}
