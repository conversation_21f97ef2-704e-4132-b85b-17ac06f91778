import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
  Join<PERSON><PERSON>,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { PracticeExercise } from './practice-exercise.entity';

@Entity('practice_exercises_medical_conditions')
export class PracticeExercisesMedicalConditions {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  practice_exersise_id: string;

  @Column()
  medical_condition_id: string;

  @ManyToOne(
    () => PracticeExercise,
    (practiceExercise) => practiceExercise.medicalConditions,
  )
  @JoinColumn({ name: 'practice_exersise_id' })
  practiceExercise: PracticeExercise;

  @ManyToOne(
    () => MedicalCondition,
    (medicalCondition) => medicalCondition.practiceExercises,
  )
  @JoinColumn({ name: 'medical_condition_id' })
  medicalCondition: MedicalCondition;
}
