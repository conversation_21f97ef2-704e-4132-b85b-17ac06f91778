import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  NotFoundException,
  Query,
} from '@nestjs/common';
import { ExerciseService } from './exercise.service';
import { CreateExerciseDto } from './dto/create-exercise.dto';
import { UpdateExerciseDto } from './dto/update-exercise.dto';
import { ResponseService } from 'src/common/services/response.service';
import { AddMedicalHistoryDto } from 'src/questions/dto/add-medical-history.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { ExerciseMedicalCondition } from './entities/exersice-medical-condition.entity';
import { ChildrenService } from 'src/children/children.service';
import { FindManyOptions, Like } from 'typeorm';

@Controller('exercise')
export class ExerciseController {
  medicalConditionService: any;
  constructor(
    private readonly exerciseService: ExerciseService,
    private readonly responseService: ResponseService,
    private readonly childrenService: ChildrenService,
  ) {}

  @UseGuards(AccessTokenGuard)
  @Post()
  async create(@Body() createExerciseDto: CreateExerciseDto) {
    const newExersice = await this.exerciseService.create(createExerciseDto);
    let exerciseMedicalConditons;
    if (createExerciseDto.medical_condition_ids) {
      exerciseMedicalConditons =
        await this.exerciseService.addMedicalConditionsToExercise(
          newExersice,
          createExerciseDto.medical_condition_ids,
        );
    }
    newExersice.exerciseMedicalConditions = exerciseMedicalConditons;
    return this.responseService.successResponse(
      'New Exersice Created',
      newExersice,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Post(':id/add-medical-condition')
  async addMedicalHistory(
    @Param('id') exersiseId: string,
    @Body() addMedicalHistoryDto: AddMedicalHistoryDto,
  ) {
    const exercise = await this.exerciseService.findOne(exersiseId);
    if (!exercise) {
      throw new NotFoundException('Question not found');
    }
    const data = await this.exerciseService.addMedicalConditionsToExercise(
      exercise,
      addMedicalHistoryDto.medical_condition_ids,
    );
    return this.responseService.successResponse(
      'Medical Conditions Added Sucessfully',
      data,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll(@Query() filter) {
    const listData = await this.exerciseService.findAll(filter);
    return this.responseService.successResponse('Exersice List', listData);
  }

  @UseGuards(AccessTokenGuard)
  @Get('/assessment')
  async findAllassessmentExersise(@Query() filter) {
    let age = null;
    if (filter.child_id) {
      const child = await this.childrenService.findOne(filter.child_id);
      if (child) {
        age = child.age;
      }
    }
    const listData = await this.exerciseService.findAllAsessmentExersise(age);
    return this.responseService.successResponse(
      'Assessment Exersice List',
      listData,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get('paginated')
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page) || 1;
    const limit = parseInt(filter.limit) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [exersises, total] = await this.exerciseService.findAndCountByParam(
      where,
      skip,
      limit,
    );

    return this.responseService.successResponse('Exersises List', {
      exersises: exersises,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @UseGuards(AccessTokenGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const Exercise = await this.exerciseService.findOne(id);
    return this.responseService.successResponse(
      'Exersise fetch Sucessfull',
      Exercise,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateExerciseDto: UpdateExerciseDto,
  ) {
    const updatedExercise = await this.exerciseService.update(
      id,
      updateExerciseDto,
    );
    let exercise;
    if (updateExerciseDto.medical_condition_ids) {
      exercise = await this.exerciseService.addMedicalConditionsToExercise(
        updatedExercise,
        updateExerciseDto.medical_condition_ids,
      );
    }
    updatedExercise.exerciseMedicalConditions =
      exercise.exerciseMedicalConditions;
    return this.responseService.successResponse(
      'Exercise Updated Sucessfully',
      updatedExercise,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Delete(':id')
  async remove(@Param('id') id: string) {
    const exercise = await this.exerciseService.findOne(id);
    if (!exercise) {
      return this.responseService.errorResponse('Exercise Not Found');
    }
    await this.exerciseService.remove(id);
    return this.responseService.successResponse('Exercise Removed Sucessfully');
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.type) {
      where['type'] = filter.type;
    }
    if (filter.text) {
      where['text'] = Like(`%${filter.text}%`);
    }
    return where;
  }
}
