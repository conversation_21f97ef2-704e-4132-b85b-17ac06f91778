import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { MatchingGame } from './matching-game.entity';

@Entity('matching_game_items')
export class MatchingGameItems {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => MatchingGame, (matchingGame) => matchingGame.items, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'matching_game_id' }) // Explicitly specify the column name
  matchingGame: MatchingGame;

  @Column()
  matching_game_id: string;

  @Column()
  item_left: string;

  @Column()
  item_right: string;
}
