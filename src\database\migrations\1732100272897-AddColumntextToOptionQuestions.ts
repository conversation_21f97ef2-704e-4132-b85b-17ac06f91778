import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumntextToOptionQuestions1732100272897
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'option_questions',
      new TableColumn({
        name: 'text',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('option_questions', 'text');
  }
}
