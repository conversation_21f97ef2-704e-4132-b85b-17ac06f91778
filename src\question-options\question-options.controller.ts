import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { QuestionOptionsService } from './question-options.service';
import { CreateQuestionOptionDto } from './dto/create-question-option.dto';
import { UpdateQuestionOptionDto } from './dto/update-question-option.dto';
import { ResponseService } from 'src/common/services/response.service';
import { filter } from 'rxjs';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';

@Controller('question-options')
export class QuestionOptionsController {
  constructor(
    private readonly questionOptionsService: QuestionOptionsService,
    private readonly responseService: ResponseService,
  ) {}

  @UseGuards(AccessTokenGuard)
  @Post()
  async create(@Body() createQuestionOptionDto: CreateQuestionOptionDto) {
    const newOption = await this.questionOptionsService.create(
      createQuestionOptionDto,
    );
    return this.responseService.successResponse(
      'New Option Added Sucessfully',
      newOption,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll(@Query() filter) {
    const listData = await this.questionOptionsService.findAll(filter);
    return this.responseService.successResponse('Options List', listData);
  }

  @UseGuards(AccessTokenGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const option = await this.questionOptionsService.findOne(id);
    if (!option) {
      return this.responseService.errorResponse('Option Not Found');
    }
    return this.responseService.successResponse('Options Found', option);
  }

  @UseGuards(AccessTokenGuard)
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateQuestionOptionDto: UpdateQuestionOptionDto,
  ) {
    const updatedOption = await this.questionOptionsService.update(
      id,
      updateQuestionOptionDto,
    );
    return this.responseService.successResponse(
      'Option Updated Sucessfully',
      updatedOption,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Delete(':id')
  async remove(@Param('id') id: string) {
    const option = await this.questionOptionsService.findOne(id);
    if (!option) {
      return this.responseService.errorResponse('Option Not Found');
    }
    await this.questionOptionsService.remove(id);
    return this.responseService.successResponse('Option Removed Sucessfully');
  }
}
