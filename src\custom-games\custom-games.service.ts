import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CustomGame } from './entities/custom-game.entity';
import { CustomGameAge } from './entities/custom-game-age.entity';
import { CreateCustomGameDto } from './dto/create-custom-game.dto';
import { UpdateCustomGameDto } from './dto/update-custom-game.dto';

@Injectable()
export class CustomGamesService {
  constructor(
    @InjectRepository(CustomGame)
    private readonly customGameRepository: Repository<CustomGame>,
    @InjectRepository(CustomGameAge)
    private readonly customGameAgeRepository: Repository<CustomGameAge>,
  ) {}

  async create(createCustomGameDto: CreateCustomGameDto) {
    const { ages, ...data } = createCustomGameDto;
    const newCustomGame = this.customGameRepository.create(data);
    const savedCustomGame = await this.customGameRepository.save(newCustomGame);

    if (ages && ages.length > 0) {
      await this.addAgesToCustomGame(savedCustomGame, ages);
    }

    return savedCustomGame;
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[CustomGame[], number]> {
    return await this.customGameRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
  }

  async findForUser(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[CustomGame[], number]> {
    return await this.customGameRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
  }

  async findOne(id: string) {
    const customGame = await this.customGameRepository.findOne({
      where: { id },
      relations: ['exerciseType', 'ages'],
    });
    if (!customGame) {
      throw new NotFoundException(`CustomGame with ID ${id} not found`);
    }
    return customGame;
  }

  async update(id: string, updateCustomGameDto: UpdateCustomGameDto) {
    const customGame = await this.findOne(id);
    const { ages, ...data } = updateCustomGameDto;

    await this.customGameRepository.update(id, data);

    if (ages && ages.length > 0) {
      await this.customGameAgeRepository.delete({ customGame });
      await this.addAgesToCustomGame(customGame, ages);
    }

    return await this.findOne(id);
  }

  async remove(id: string) {
    return await this.customGameRepository.delete(id);
  }

  async addAgesToCustomGame(customGame: CustomGame, ages: number[]) {
    const ageEntities = ages.map((age) => {
      const ageEntity = new CustomGameAge();
      ageEntity.age = age;
      ageEntity.customGame = customGame;
      return ageEntity;
    });
    return await this.customGameAgeRepository.save(ageEntities);
  }
}
