import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'typeorm';
import { OptionQuestion } from './option-question.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';

@Entity('option_questions_medical_conditions') // Define the table name
export class OptionQuestionsMedicalConditions {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(
    () => OptionQuestion, // Many-to-one relation with OptionQuestion
    (optionQuestion) => optionQuestion.medicalConditions,
  )
  @JoinColumn({ name: 'option_question_id' })
  optionQuestion: OptionQuestion;

  @ManyToOne(() => MedicalCondition) // Many-to-one relation with MedicalCondition
  @JoinColumn({ name: 'medical_condition_id' })
  medicalCondition: MedicalCondition;
}
