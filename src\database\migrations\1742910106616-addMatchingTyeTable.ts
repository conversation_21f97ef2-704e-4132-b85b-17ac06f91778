import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMatchingTyeTable1742910106616 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `exercise_types` MODIFY COLUMN `exercise` ENUM("practice_exercise","option_question","downloadable_question","hear_and_select","speed_typing","matching_game") DEFAULT "practice_exercise";',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `exercise_types` MODIFY COLUMN `exercise` ENUM("practice_exercise","option_question","downloadable_question","hear_and_select","speed_typing) DEFAULT "practice_exercise";',
    );
  }
}
