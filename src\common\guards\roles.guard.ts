import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UsersService } from 'src/users/users.service';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private userService: UsersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const roles = this.reflector.get<string[]>('roles', context.getHandler());

    const request = context.switchToHttp().getRequest();

    if (request?.user) {
      const { sub } = request.user;
      const user = await this.userService.findOneByParam({ id: sub });
      const userRoles = user.user_type;
      if (roles.includes(userRoles)) {
        return true;
      }
      // for (const userRole of userRoles) {
      //   console.log(userRole);
      //   if (roles.includes(userRole)) {
      //     return true;
      //   }
      // }
    }

    return false;
  }
}
