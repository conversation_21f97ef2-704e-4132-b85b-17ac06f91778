import { Modu<PERSON> } from '@nestjs/common';
import { MedicalConditionService } from './medical-condition.service';
import { MedicalConditionController } from './medical-condition.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MedicalCondition } from './entities/medical-condition.entity';
import { CommonModule } from 'src/common/common.module';
import { JwtModule } from '@nestjs/jwt';
import { UsersModule } from 'src/users/users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MedicalCondition]),
    CommonModule,
    UsersModule,
  ],
  controllers: [MedicalConditionController],
  providers: [MedicalConditionService],
  exports: [MedicalConditionService],
})
export class MedicalConditionModule {}
