import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey,
} from 'typeorm';

export class UpdatePracticeExercisesStructure1732027011082
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'practice_exercises',
      new TableColumn({
        name: 'level_id',
        type: 'varchar',
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      'practice_exercises',
      new TableForeignKey({
        columnNames: ['level_id'],
        referencedTableName: 'practice_exercises_levels',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL', // Allow soft unlinking if the level is deleted
      }),
    );

    const table = await queryRunner.getTable('practice_exercises_levels');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('practice_exercise_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('practice_exercises_levels', foreignKey);
    }

    await queryRunner.dropColumn(
      'practice_exercises_levels',
      'practice_exercise_id',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('practice_exercises');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('level_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('practice_exercises', foreignKey);
    }

    await queryRunner.dropColumn('practice_exercises', 'level_id');
    await queryRunner.addColumn(
      'practice_exercises_levels',
      new TableColumn({
        name: 'practice_exercise_id',
        type: 'varchar',
        isNullable: false,
      }),
    );

    // await queryRunner.createForeignKey(
    //   'practice_exercises_levels',
    //   new TableForeignKey({
    //     columnNames: ['practice_exercise_id'],
    //     referencedTableName: 'practice_exercises',
    //     referencedColumnNames: ['id'],
    //     onDelete: 'SET NULL',
    //   }),
    // );
  }
}
