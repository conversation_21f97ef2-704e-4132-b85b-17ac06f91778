import { Injectable } from '@nestjs/common';
import { EngagespotClient } from '@engagespot/node';
import {
  ES_SENT_OTP,
  ENGAGESPOT_API_KEY,
  ENGAGESPOT_API_SECRET,
  ES_SEND_PASSWORD,
} from './constants/engagespot.constants';

@Injectable()
export class EngagespotService {
  engageSpotClient() {
    return EngagespotClient({
      apiKey: ENGAGESPOT_API_KEY,
      apiSecret: ENGAGESPOT_API_SECRET,
    });
  }

  async createOrUpdateUser(user) {
    const identifier = user.email;
    // console.log(user)
    try {
      const client = await this.engageSpotClient();
      // console.log(`trying to create/update user`);
      await client.createOrUpdateUser(identifier, {
        // phoneNumber: user.phone ?? null,
        name: user.name,
        email: user.email ?? null,
      });
      // console.log('user create/update complete');
      return true;
    } catch (err) {
      console.log(`error: ${err.message}`);
      return false;
    }
  }

  async sendNotification(recipients: any, type: string, data?: object) {
    let template;

    switch (type) {
      case 'sign-up':
        template = process.env.ES_DRIVER_ACCEPTED_NOTIFY;
        break;

      case 'forgot-password':
        template = process.env.ES_ASSIGN_DRIVER;
        break;

      case 'test':
        template = process.env.ES_TEST;
        break;

      default:
        template = null;
        break;
    }
    await this.send(recipients, template, data);
  }

  async getWorkflowIdentifier(type: string) {
    let workflowIdentifier: string;

    switch (type) {
      case 'sent_otp':
        workflowIdentifier = ES_SENT_OTP;
        break;
      case 'send_password':
        workflowIdentifier = ES_SEND_PASSWORD;
        break;
      default:
        workflowIdentifier = null;
    }
    return workflowIdentifier;
  }

  async send(
    type: string,
    recipients: Array<string>,
    data?: object,
    sendAt?: string,
  ) {
    const workflowIdentifier = await this.getWorkflowIdentifier(type);
    try {
      const client = await this.engageSpotClient();
      console.log(data);
      client.send({
        notification: {
          workflow: { identifier: workflowIdentifier },
        },
        data: data,
        sendTo: {
          recipients,
        },
        sendAt,
      });

      return true;
    } catch (err) {
      console.error(err.message);
      return false;
    }
  }
}
