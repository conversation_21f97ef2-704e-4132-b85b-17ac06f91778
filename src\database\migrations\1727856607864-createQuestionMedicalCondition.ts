import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateQuestionMedicalCondition1727856607864
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'question_medical_condition',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'question_id',
            type: 'varchar',
          },
          {
            name: 'medical_condition_id',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
    );
    await queryRunner.createForeignKey(
      'question_medical_condition',
      new TableForeignKey({
        columnNames: ['question_id'],
        referencedTableName: 'questions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
    await queryRunner.createForeignKey(
      'question_medical_condition',
      new TableForeignKey({
        columnNames: ['medical_condition_id'],
        referencedTableName: 'medical_condition',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('question_medical_condition');
    const questionForegnkey = table.foreignKeys.find((fk) =>
      fk.columnNames.indexOf('question_id'),
    );
    const MedicalConditionForenkey = table.foreignKeys.find((fk) =>
      fk.columnNames.indexOf('medical_condition_id'),
    );
    await queryRunner.dropForeignKey(
      'question_medical_condition',
      questionForegnkey,
    );
    await queryRunner.dropForeignKey(
      'question_medical_condition',
      MedicalConditionForenkey,
    );
    await queryRunner.dropTable('question_medical_condition');
  }
}
