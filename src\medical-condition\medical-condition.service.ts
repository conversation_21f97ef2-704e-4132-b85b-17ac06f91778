import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateMedicalConditionDto } from './dto/create-medical-condition.dto';
import { UpdateMedicalConditionDto } from './dto/update-medical-condition.dto';
import { CreateMedicalConditionInterface } from './interface/medical-condition.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { MedicalCondition } from './entities/medical-condition.entity';
import { Repository } from 'typeorm';
import { take } from 'rxjs';

@Injectable()
export class MedicalConditionService {
  constructor(
    @InjectRepository(MedicalCondition)
    private medicalConditionRepository: Repository<MedicalCondition>,
  ) {}
  async create(
    createMedicalConditionInterface: CreateMedicalConditionInterface,
  ) {
    const newMedicalCondition = this.medicalConditionRepository.create(
      createMedicalConditionInterface,
    );
    return await this.medicalConditionRepository.save(newMedicalCondition);
  }

  async findAll() {
    return await this.medicalConditionRepository.find();
  }

  async findByIds(medical_condition_ids: string[]) {
    return await this.medicalConditionRepository.findByIds(
      medical_condition_ids,
    );
  }

  async findByParam(params: { [key: string]: any }, relations?: string[]) {
    return await this.medicalConditionRepository.find({
      where: params,
      relations,
    });
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ) {
    const [MedicalConditions, total] =
      await this.medicalConditionRepository.findAndCount({
        where: params,
        relations,
        skip,
        take,
      });
    return [MedicalConditions, total];
  }

  async findOne(id: string) {
    return await this.medicalConditionRepository.findOne({ where: { id } });
  }

  async update(
    id: string,
    updateMedicalConditionDto: UpdateMedicalConditionDto,
  ): Promise<MedicalCondition> {
    const medicalCondition = await this.findOne(id);
    if (!medicalCondition) {
      throw new NotFoundException(`Medical condition with ID ${id} not found`);
    }
    medicalCondition.name = updateMedicalConditionDto.name;
    return this.medicalConditionRepository.save(medicalCondition);
  }

  async remove(id: string) {
    return await this.medicalConditionRepository.delete(id);
  }
}
