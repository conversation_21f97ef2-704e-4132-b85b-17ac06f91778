import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ExerciseType } from '../../exercise-types/entities/exercise-type.entity';
import { SpeedTypingAge } from './speed-typing-age.entity';

@Entity('speed_typing')
export class SpeedTyping {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  exercise_type_id: string;

  @Column()
  question: string;

  @Column({ type: 'json', nullable: true })
  options: Record<string, any> | null;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'int', nullable: true })
  time_limit: number;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  //relations
  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.hearAndSelects,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'exercise_type_id' }) // Explicitly specify the column name
  exerciseType: ExerciseType;

  @OneToMany(
    () => SpeedTypingAge,
    (speedTypingAge) => speedTypingAge.speedTyping,
  )
  ages: SpeedTypingAge[];
}
