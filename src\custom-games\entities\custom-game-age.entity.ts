import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON>inC<PERSON>um<PERSON>,
} from 'typeorm';
import { CustomGame } from './custom-game.entity';

@Entity('custom_game_age')
export class CustomGameAge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => CustomGame, (customGame) => customGame.id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'custom_game_id' }) // Explicitly specify the column name
  customGame: CustomGame;

  @Column({ type: 'int' })
  age: number;

  @Column()
  custom_game_id: string;
}
