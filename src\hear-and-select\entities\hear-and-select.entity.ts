import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ExerciseType } from '../../exercise-types/entities/exercise-type.entity';
import { HearAndSelectAge } from './hear-and-select-age.entity';

export enum HearAndSelectType {
  CHOICE_BASED = 'choice_based',
  TEXT_BASED = 'text_based',
}

@Entity('hear_and_select')
export class HearAndSelect {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  exercise_type_id: string;

  @Column({ type: 'enum', enum: HearAndSelectType })
  type: HearAndSelectType;

  @Column()
  audio_file: string;

  @Column({ type: 'json', nullable: true })
  options: Record<string, any> | null;

  @Column({ nullable: true })
  correct_answer: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  text: string;

  @Column({ type: 'int', nullable: true })
  time_limit: number;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // relations
  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.hearAndSelects,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'exercise_type_id' }) // Explicitly specify the column name
  exerciseType: ExerciseType;

  @OneToMany(
    () => HearAndSelectAge,
    (hearAndSelectAge) => hearAndSelectAge.hearAndSelect,
  )
  ages: HearAndSelectAge[];
}
