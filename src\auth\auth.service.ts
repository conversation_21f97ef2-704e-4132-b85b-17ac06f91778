import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
  RequestTimeoutException,
  UnauthorizedException,
} from '@nestjs/common';
import { UsersService } from 'src/users/users.service';
import { JwtService } from '@nestjs/jwt';
import { User } from 'src/users/entities/user.entity';
import { SignInDto } from './dto/signIn.dto';
import { AuthTokenervice } from './services/auth-token.service';
import * as bcrypt from 'bcrypt';
import { ResponseService } from 'src/common/services/response.service';
import { OtpService } from 'src/otp/otp.service';
import { UserOtpService } from 'src/user-otp/user-otp.service';
import { SetForgotPasswordDto } from './dto/forget-password-change.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { EngagespotService } from 'src/engagespot/engagespot.service';

export function encodedPassword(rawPassword: string) {
  const SALT = bcrypt.genSaltSync();
  return bcrypt.hashSync(rawPassword, SALT);
}

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private authTokenService: AuthTokenervice,
    private responseService: ResponseService,
    private otpService: OtpService,
    private userOtpService: UserOtpService,
    private engagespotService: EngagespotService,
  ) {}
  async signIn(signInDto: SignInDto, headers: any) {
    const user = await this.validateUser(signInDto, headers);
    const tokens = await this.authTokenService.generateTokens(
      user.id,
      user.email,
      user.user_type,
    );
    return this.responseService.successResponse('User logged successfully', {
      user,
      tokens,
    });
  }

  // async resentOtp(user_id: string) {
  //   const user = await this.usersService.findOneByParam({ id: user_id }, [
  //     'otp',
  //   ]);
  //   if (!user) {
  //     throw new NotFoundException('User not found');
  //   }
  // }

  async verifyOtp(user_id: string, otp: string) {
    const user = await this.usersService.findOneByParam({ id: user_id }, [
      'otp',
    ]);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    if (!user.otp) {
      throw new ConflictException('generate OTP for this user first');
    }
    const isValidOtp = await this.otpService.validateOtp(
      otp,
      user.otp.otp_secret,
    );
    if (isValidOtp) {
      const tokens = await this.authTokenService.generateTokens(
        user.id,
        user.email,
        user.user_type,
      );
      const passwordToken = await this.authTokenService.generatePassToken(
        user.id,
      );
      user.is_email_verified = true;
      await this.usersService.saveUser(user);
      await this.userOtpService.deleteUserOtpByUserId(user_id);
      return this.responseService.successResponse('OTP verified', {
        user,
        tokens,
        passwordToken,
      });
    } else {
      throw new UnauthorizedException('Invalid OTP');
    }
  }

  async forgotPassword(email: string) {
    const user = await this.usersService.findUserByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const recipents = [user.email];
    const otpSecret = this.otpService.generateSecret();
    const otp = this.otpService.generateOtp(otpSecret);
    //console.log(user.id);
    await this.userOtpService.createNewUserOtpSecret(user.id, otpSecret);
    const data = { otp: otp, name: user.name };
    await this.engagespotService.createOrUpdateUser(user);
    await this.engagespotService.send('sent_otp', [user.email], data);
    return this.responseService.successResponse(
      `Otp successfully send to your email`, //OTP: ${otp}
      user,
    );
  }

  async SetForgotPassword(
    userId: string,
    setForgotPasswordDto: SetForgotPasswordDto,
  ) {
    const { new_password, password_token } = setForgotPasswordDto;

    // Validate the password token
    const tokenUserId =
      await this.authTokenService.validatePasswordToken(password_token);
    if (tokenUserId !== userId) {
      throw new UnauthorizedException('Invalid password token');
    }
    const user = await this.usersService.findOneByParam({ id: userId }, [
      'otp',
    ]);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    user.password = await encodedPassword(new_password);
    const savedUser = await this.usersService.saveUser(user);
    delete savedUser.password;
    return this.responseService.successResponse(
      'Password changed successfully',
      savedUser,
    );
  }

  async ChangePassword(changePassdto: ChangePasswordDto, userId: string) {
    const user = await this.usersService.findOne(userId);
    if (!user) {
      throw new NotFoundException('User Not Found');
    }
    if (changePassdto.new_password === changePassdto.current_password) {
      throw new BadRequestException('New & old password connot be same');
    }
    if (changePassdto.new_password !== changePassdto.confirm_password) {
      throw new BadRequestException('password mismatch');
    }
    const passCheck = await bcrypt.compare(
      changePassdto.current_password,
      user.password,
    );
    if (!passCheck) {
      throw new UnauthorizedException('Incorrect password, try again');
    }
    user.password = await encodedPassword(changePassdto.new_password);
    const savedUser = await this.usersService.saveUser(user);
    delete savedUser.password;
    return this.responseService.successResponse(
      'Password Change Success',
      savedUser,
    );
  }

  async validateUser(signInDto: SignInDto, headers: any): Promise<User> {
    const { email, password } = signInDto;
    const appType = headers['x_app_type'];

    const user = await this.usersService.findOneByParam({ email: email });
    if (!user || !user.password) {
      throw new UnauthorizedException({
        status: false,
        message: 'Invalid email or password',
      });
    }
    if (user.is_email_verified == false && user.user_type !== 'super_admin') {
      throw new UnauthorizedException('Invalid email');
    }
    const passCheck = await bcrypt.compare(password, user.password);
    if (!passCheck) {
      throw new UnauthorizedException('Incorrect password');
    }

    // app type validation need to implement in future
    // const userRoles = user.roles.map((role) => role.name);
    // if (appType && !userRoles.includes(appType)) {
    //   throw new UnauthorizedException({
    //     status: false,
    //     message: 'You do not have permission to access this resource',
    //   });
    // }

    return user;
  }

  async me(id: string) {
    const user = await this.usersService.findOneByParam({ id });
    return this.responseService.successResponse('Authenticated user', user);
  }
}
