import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateAssessmenQuestionTable1730102365530
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'assessment_questions',
      'answer',
      new TableColumn({
        name: 'answer',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'assessment_questions',
      'answer',
      new TableColumn({
        name: 'answer',
        type: 'varchar',
        isNullable: false,
      }),
    );
  }
}
