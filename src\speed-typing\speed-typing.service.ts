import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateSpeedTypingDto } from './dto/create-speed-typing.dto';
import { UpdateSpeedTypingDto } from './dto/update-speed-typing.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { SpeedTyping } from './entities/speed-typing.entity';
import { Repository } from 'typeorm';
import { SpeedTypingAge } from './entities/speed-typing-age.entity';

@Injectable()
export class SpeedTypingService {
  constructor(
    @InjectRepository(SpeedTyping)
    private readonly speedTypingRepository: Repository<SpeedTyping>,
    @InjectRepository(SpeedTypingAge)
    private readonly speedTypingAgeRepository: Repository<SpeedTypingAge>,
  ) {}
  async create(createSpeedTypingDto: CreateSpeedTypingDto) {
    const { ages, level, ...data } = createSpeedTypingDto;

    const newHearAndSelect = this.speedTypingRepository.create(data);
    return await this.speedTypingRepository.save(newHearAndSelect);
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[SpeedTyping[], number]> {
    const [questionTypes, total] =
      await this.speedTypingRepository.findAndCount({
        where: params,
        relations,
        skip,
        take,
      });
    return [questionTypes, total];
  }
  findAll() {
    return `This action returns all speedTyping`;
  }

  async findOne(id: string) {
    const speedTyping = await this.speedTypingRepository.findOne({
      where: { id },
      relations: ['exerciseType', 'ages'],
    });
    if (!speedTyping) {
      throw new NotFoundException(`SpeedTyping with ID ${id} not found`);
    }
    return speedTyping;
  }

  async update(id: string, updateSpeedTypingDto: UpdateSpeedTypingDto) {
    const hearAndSelect = await this.findOne(id);
    const { ages, level, ...data } = updateSpeedTypingDto;
    await this.speedTypingRepository.update(id, data);
    return hearAndSelect;
  }

  async remove(id: string) {
    //delete hear and select
    return await this.speedTypingRepository.delete(id);
  }

  async addAgeToSpeedTyping(speedTyping: SpeedTyping, ages: number[]) {
    await this.speedTypingAgeRepository.delete({ speedTyping });
    const ageEntities = ages.map((age) => {
      const ageEntity = new SpeedTypingAge();
      ageEntity.age = age;
      ageEntity.speedTyping = speedTyping; // Use the correct property name
      return ageEntity;
    });
    return await this.speedTypingAgeRepository.save(ageEntities);
  }
}
