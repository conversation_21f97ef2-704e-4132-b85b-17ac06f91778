import {
  IsEmail,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
} from 'class-validator';
import { UserType } from '../entities/user.entity';

export class SignUpDto {
  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsPhoneNumber()
  phone_number?: string;

  @IsIn(['parent', 'teacher'])
  user_type: UserType;

  @IsString()
  password: string;
}
