import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  BadRequestException,
  Query,
  NotFoundException,
  UseGuards,
} from '@nestjs/common';
import { OptionQuestionsService } from './option-questions.service';
import { CreateOptionQuestionDto } from './dto/create-option-question.dto';
import { UpdateOptionQuestionDto } from './dto/update-option-question.dto';
import { ExerciseTypesService } from 'src/exercise-types/exercise-types.service';
import { ExerciseEnum } from 'src/exercise-types/entities/exercise-type.entity';
import { ResponseService } from 'src/common/services/response.service';
import { MedicalConditionService } from 'src/medical-condition/medical-condition.service';
import { FindManyOptions, In } from 'typeorm';
import { filter } from 'rxjs';
import { Console } from 'console';
import { Role } from 'src/common/decorators/roles.decorator';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';

@Controller('option-questions')
export class OptionQuestionsController {
  constructor(
    private readonly optionQuestionsService: OptionQuestionsService,
    private readonly exersiseTypesService: ExerciseTypesService,
    private readonly responseService: ResponseService,
    private readonly medicalConditionService: MedicalConditionService,
  ) {}

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post()
  async create(@Body() createOptionQuestionDto: CreateOptionQuestionDto) {
    const exercise_type = await this.exersiseTypesService.findOne(
      createOptionQuestionDto.exercise_type_id,
    );
    if (exercise_type.exercise !== ExerciseEnum.OPTION_QUESTION) {
      throw new BadRequestException('Invalid Exercise Type');
    }
    const level = createOptionQuestionDto.level;
    delete createOptionQuestionDto.level;
    const savedOptionQuestions = await this.optionQuestionsService.create({
      ...createOptionQuestionDto,
      exerciseType: exercise_type,
    });
    delete savedOptionQuestions.ages;
    const optionQuestionAges =
      await this.optionQuestionsService.addAgeToOptionQuestion(
        savedOptionQuestions,
        createOptionQuestionDto.ages,
      );

    //revoved level and medical conditions

    // if (
    //   createOptionQuestionDto.medical_condition_ids &&
    //   createOptionQuestionDto.medical_condition_ids.length !== 0
    // ) {
    //   const medicalConditions = await this.medicalConditionService.findByParam({
    //     id: In(createOptionQuestionDto.medical_condition_ids),
    //   });
    //   await this.optionQuestionsService.addMedicalConditions(
    //     savedOptionQuestions.id,
    //     medicalConditions,
    //   );
    // }
    // await this.optionQuestionsService.addMedicalConditions(
    //   savedOptionQuestions.id,
    //   exercise_type.medicalConditions,
    // );

    // await this.optionQuestionsService.checkAndUpdateRankList(
    //   level,
    //   exercise_type,
    //   savedOptionQuestions,
    // );
    savedOptionQuestions.ages = optionQuestionAges;
    return this.responseService.successResponse(
      'New Option Question Created Successfully',
      savedOptionQuestions,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll(@Query() filter) {
    const page = parseInt(filter.page) || 1;
    const limit = parseInt(filter.limit) || 10;
    const skip = (page - 1) * limit;

    let where: FindManyOptions['where'] = {};

    const [optionQuestions, total] =
      await this.optionQuestionsService.findAll(filter);

    return this.responseService.successResponse('Option Question List', {
      option_questions: optionQuestions,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get('users')
  async findforChild(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [data, total] = await this.optionQuestionsService.findAndCountByParam(
      where,
      skip,
      limit,
      ['exerciseType'],
    );

    return this.responseService.successResponse('HearAndSelect List', {
      data: data,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @UseGuards(AccessTokenGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const optionQuestion = await this.optionQuestionsService.findOne(id);
    if (!optionQuestion) {
      throw new NotFoundException('Option Question Not Found');
    }
    return this.responseService.successResponse(
      'Option Question Details',
      optionQuestion,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateOptionQuestionDto: UpdateOptionQuestionDto,
  ) {
    const existingOptionQuestion =
      await this.optionQuestionsService.findOne(id);
    if (!existingOptionQuestion) {
      throw new BadRequestException('Option Question not found');
    }
    // Check if exercise type is valid
    const exerciseType = await this.exersiseTypesService.findOne(
      updateOptionQuestionDto.exercise_type_id,
    );
    if (exerciseType.exercise !== ExerciseEnum.OPTION_QUESTION) {
      throw new BadRequestException('Invalid Exercise Type');
    }

    // Update the OptionQuestion properties
    const updatedOptionQuestion = await this.optionQuestionsService.update(
      existingOptionQuestion,
      {
        ...updateOptionQuestionDto,
        exerciseType: exerciseType,
      },
    );
    if (updateOptionQuestionDto.ages && updateOptionQuestionDto.ages.length) {
      updatedOptionQuestion.ages =
        await this.optionQuestionsService.addAgeToOptionQuestion(
          updatedOptionQuestion,
          updateOptionQuestionDto.ages,
        );
    }

    //revoved level and medical conditions from update
    // // Update medical conditions if provided
    // if (
    //   updateOptionQuestionDto.medical_condition_ids &&
    //   updateOptionQuestionDto.medical_condition_ids.length !== 0
    // ) {
    //   const medicalConditions = await this.medicalConditionService.findByParam({
    //     id: In(updateOptionQuestionDto.medical_condition_ids),
    //   });
    //   await this.optionQuestionsService.updateMedicalConditions(
    //     updatedOptionQuestion.id,
    //     medicalConditions,
    //   );
    // }

    // // Update level and adjust ranking if needed
    // if (updateOptionQuestionDto.level) {
    //   await this.optionQuestionsService.checkAndUpdateRankList(
    //     updateOptionQuestionDto.level,
    //     exerciseType,
    //     updatedOptionQuestion,
    //   );
    // }

    // Return a success response with the updated OptionQuestion
    return this.responseService.successResponse(
      'Option Question updated successfully',
      updatedOptionQuestion,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    const optionQuestion = await this.optionQuestionsService.findOne(id);
    if (!optionQuestion) {
      throw new NotFoundException('Option Question not found');
    }
    await this.optionQuestionsService.remove(id);
    return this.responseService.successResponse(
      'Option Question deleted successfully',
    );
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.ages) {
      const agesArray = Array.isArray(filter.ages)
        ? filter.ages
        : filter.ages.split(',').map((age) => parseInt(age.trim(), 10));
      where['ages'] = { age: In(agesArray) };
    }
    if (filter.exercise_type_id) {
      where['exercise_type_id'] = filter.exercise_type_id;
    }
    if (filter.type) {
      where['type'] = filter.type;
    }
    return where;
  }
}
