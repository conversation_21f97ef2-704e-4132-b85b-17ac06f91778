import { Module } from '@nestjs/common';
import { OptionQuestionsService } from './option-questions.service';
import { OptionQuestionsController } from './option-questions.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OptionQuestionsAge } from './entities/option-questions-age.entity';
import { OptionQuestionsMedicalConditions } from './entities/option-question-medical-condition.entity';
import { OptionQuestion } from './entities/option-question.entity';
import { ExerciseTypesModule } from 'src/exercise-types/exercise-types.module';
import { CommonModule } from 'src/common/common.module';
import { MedicalConditionModule } from 'src/medical-condition/medical-condition.module';
import { UsersModule } from 'src/users/users.module';
import { OptionQuestionLevel } from './entities/option-questions-level.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OptionQuestion,
      OptionQuestionsMedicalConditions,
      OptionQuestionsAge,
      OptionQuestionLevel,
    ]),
    ExerciseTypesModule,
    UsersModule,
    CommonModule,
    MedicalConditionModule,
  ],
  controllers: [OptionQuestionsController],
  providers: [OptionQuestionsService],
})
export class OptionQuestionsModule {}
