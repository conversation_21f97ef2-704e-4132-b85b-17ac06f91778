import {
  MigrationInterface,
  QueryRunner,
  Table<PERSON><PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class RemoveColumsFormQuestionExersice1732525212694
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const downloadableQuestionsTable = await queryRunner.getTable(
      'downloadable_questions',
    );
    const downloadableQuestionsLevelsTable = await queryRunner.getTable(
      'downloadable_questions_levels',
    );
    const foreignKey1 = downloadableQuestionsTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('question_type_id') !== -1,
    );
    const foreignKey2 = downloadableQuestionsLevelsTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('downloadable_question_types_id') !== -1,
    );
    if (foreignKey1) {
      await queryRunner.dropForeignKey('downloadable_questions', foreignKey1);
    }
    if (foreignKey2) {
      await queryRunner.dropForeignKey(
        'downloadable_questions_levels',
        foreignKey2,
      );
    }
    await queryRunner.dropColumn(
      'downloadable_questions_levels',
      'downloadable_question_types_id',
    );
    await queryRunner.dropColumn('downloadable_questions', 'question_type_id');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'downloadable_questions',
      new TableColumn({
        name: 'question_type_id',
        type: 'varchar',
        isNullable: true,
      }),
    );
    await queryRunner.createForeignKey(
      'downloadable_questions',
      new TableForeignKey({
        columnNames: ['question_type_id'],
        referencedTableName: 'question_types',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );
    await queryRunner.addColumn(
      'downloadable_questions_levels',
      new TableColumn({
        name: 'downloadable_question_types_id',
        type: 'varchar',
        isNullable: true,
      }),
    );
    await queryRunner.createForeignKey(
      'downloadable_questions_levels',
      new TableForeignKey({
        columnNames: ['downloadable_question_types_id'],
        referencedTableName: 'downloadable_question_types',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );
  }
}
