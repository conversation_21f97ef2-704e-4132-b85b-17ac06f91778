import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { PracticeExercise } from './practice-exercise.entity';

@Entity('practice_exercise_ages')
export class PracticeExerciseAge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('int')
  age: number;

  @Column('varchar')
  practice_exercise_id: string;

  @ManyToOne(
    () => PracticeExercise,
    (practiceExercise) => practiceExercise.ages,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'practice_exercise_id' })
  practiceExercise: PracticeExercise;
}
