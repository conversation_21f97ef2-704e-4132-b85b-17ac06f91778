import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePlanPricesTable1746705192612 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'plan_prices',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'plan_id',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'name',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'longtext',
            isNullable: true,
          },
          {
            name: 'thumbnail',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'stripe_price_id',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'price',
            type: 'decimal',
            precision: 20,
            scale: 2,
            default: 0.0,
            isNullable: true,
          },
          {
            name: 'strike_through_price',
            type: 'decimal',
            precision: 20,
            scale: 2,
            default: 0.0,
            isNullable: true,
          },
          {
            name: 'interval',
            type: 'int',
            default: 1,
            isNullable: true,
          },
          {
            name: 'period',
            type: 'enum',
            enum: ['monthly', 'daily', 'yearly', 'weekly'],
            enumName: 'periodEnum',
            default: '"monthly"',
          },
          {
            name: 'is_published',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
    );
    await queryRunner.query(
      'ALTER TABLE `plan_prices` ADD FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE SET NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('plan_prices');
  }
}
