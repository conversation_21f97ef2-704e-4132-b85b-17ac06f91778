import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateExersiceTable1728888206622 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'exercise',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'type',
            type: 'enum',
            enum: [
              'tracing',
              'drawing',
              'puzzle',
              'route_mapping',
              'similar_figures',
            ],
            isNullable: false,
          },
          {
            name: 'shape',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'text',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('exercise');
  }
}
