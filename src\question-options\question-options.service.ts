import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateQuestionOptionDto } from './dto/create-question-option.dto';
import { UpdateQuestionOptionDto } from './dto/update-question-option.dto';
import { CreateQuestionOptionInterface } from './interface/create-question-options.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { QuestionOption } from './entities/question-option.entity';
import { Repository } from 'typeorm';

@Injectable()
export class QuestionOptionsService {
  constructor(
    @InjectRepository(QuestionOption)
    private readonly questionOptionRepository: Repository<QuestionOption>,
  ) {}

  async create(createQuestionOptionInterface: CreateQuestionOptionInterface) {
    const newOption = this.questionOptionRepository.create(
      createQuestionOptionInterface,
    );
    return await this.questionOptionRepository.save(newOption);
  }

  async findAll(filter: any) {
    let where: any = {};
    if (filter.type) {
      where = { type: filter.type };
    }
    if (filter.question_type) {
      where = { question_type: filter.question_type };
    }
    return await this.questionOptionRepository.find({ where });
  }

  async findOne(id: string) {
    return await this.questionOptionRepository.findOne({ where: { id } });
  }

  async update(id: string, updateQuestionOptionDto: UpdateQuestionOptionDto) {
    const exercise = await this.questionOptionRepository.findOne({
      where: { id },
    });
    if (!exercise) {
      throw new NotFoundException(`Exercise with ID ${id} not found`);
    }
    const updatedExercise = Object.assign(exercise, updateQuestionOptionDto);
    return await this.questionOptionRepository.save(updatedExercise);
  }

  async remove(id: string) {
    return await this.questionOptionRepository.delete(id);
  }
}
