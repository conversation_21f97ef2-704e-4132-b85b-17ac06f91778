import {
  IsArray,
  ValidateNested,
  IsUUID,
  IsString,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';

class AnswerDto {
  @IsUUID()
  @IsNotEmpty()
  question_id: string;

  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  answer: string;
}

export class UploadAssessmentAnswersDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AnswerDto)
  answers: AnswerDto[];
}
