import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>um<PERSON>,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';

@Entity('assessment_medical_condition')
export class AssessmentMedicalCondition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  assessment_id: string;

  @Column({ type: 'varchar' })
  medical_condition_id: string;

  @ManyToOne(
    () => Assessment,
    (assessment) => assessment.assessmentMedicalConditions,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'assessment_id' })
  assessment: Assessment;

  @ManyToOne(
    () => MedicalCondition,
    (medicalCondition) => medicalCondition.assessmentMedicalConditions,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'medical_condition_id' })
  medicalCondition: MedicalCondition;
}
