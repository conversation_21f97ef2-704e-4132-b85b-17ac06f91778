import * as PDFDocument from 'pdfkit';

export function getScoreLabel(score: number): string {
  if (score < 60) return 'Poor';
  if (score >= 60 && score < 70) return 'Below Average';
  if (score >= 70 && score < 85) return 'Average';
  return 'Above Average';
}

export function createSummaryReportPDF(reportData: any): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const doc = new PDFDocument({
      size: 'LETTER',
      bufferPages: true,
    });

    const buffers: Buffer[] = [];

    doc.on('data', buffers.push.bind(buffers));
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(buffers);
      resolve(pdfBuffer);
    });

    doc
      .fontSize(20)
      .text('Scribblesense Assessment Report', { align: 'center' });
    doc.moveDown();

    doc
      .fontSize(12)
      .text(`Test Date: ${reportData.testDate}`, { align: 'right' });
    doc.moveDown(2);

    doc.fontSize(14).text(`Name: ${reportData.name}`);
    doc.text(`Date of Birth: ${reportData.dob}`);
    doc.text(`Age: ${reportData.age}`);
    doc.text(`Gender: ${reportData.gender}`);
    doc.text(`School Year / Educational Level: ${reportData.schoolYear}`);
    doc.text(`Language of Education: ${reportData.language}`);
    doc.text(`Country: ${reportData.country}`);
    doc.moveDown();

    doc.text(
      `${reportData.name} has participated in a focused assessment to identify potential signs of writing difficulty that affect writing skills. This report summarizes their performance on writing-related tasks, along with feedback from their parent, highlighting any concerns about their writing ability.`,
      { align: 'justify' },
    );
    doc.moveDown(2);

    doc.fontSize(16).text('Results Summary', { underline: true });
    doc.moveDown();

    doc.fontSize(14).text('Overall Performance:');
    doc
      .fontSize(12)
      .text(
        `Tracing Exercise Score: ${getScoreLabel(reportData.tracingScore)}`,
      );
    doc.text(
      `Image Tracing Score: ${getScoreLabel(reportData.imageTracingScore)}`,
    );
    doc.moveDown();
    doc.fontSize(16).text('Areas of Concern', { underline: true });
    reportData.areasOfConcern.forEach((concern: string) => {
      doc.fontSize(12).text(`- ${concern}`);
    });

    doc.end();

    doc.on('error', (err) => {
      console.error('PDFKit Error:', err);
      reject(err);
    });
  });
}
