import {
  <PERSON>,
  Get,
  Post,
  Body,
  Headers,
  UseGuards,
  Req,
  Param,
  ConflictException,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { SignInDto } from './dto/signIn.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { Request } from 'express';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import {
  forgotPasswordDto,
  SetForgotPasswordDto,
} from './dto/forget-password-change.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  signIn(@Body() signInDto: SignInDto, @Headers() headers: any) {
    return this.authService.signIn(signInDto, headers);
  }

  @Post('verify-otp')
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    return await this.authService.verifyOtp(
      verifyOtpDto.user_id,
      verifyOtpDto.otp,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Post('change-password')
  ChangePassword(
    @Body() changepassdto: ChangePasswordDto,
    @Req() req: Request,
  ) {
    const userId = req['uid'];
    return this.authService.ChangePassword(changepassdto, userId);
  }

  @Post('forgot-password')
  forgotPassword(@Body() forgotPassword: forgotPasswordDto) {
    return this.authService.forgotPassword(forgotPassword.email);
  }

  @UseGuards(AccessTokenGuard)
  @Post('set-forgot-password')
  SetForgotPassword(
    @Body() setForgotPass: SetForgotPasswordDto,
    @Req() req: Request,
  ) {
    if (setForgotPass.new_password !== setForgotPass.confirm_password) {
      throw new ConflictException('password missmatch');
    }
    return this.authService.SetForgotPassword(req['uid'], setForgotPass);
  }

  @UseGuards(AccessTokenGuard)
  @Get('me')
  findOne(@Req() req: Request) {
    return this.authService.me(req['uid']);
  }
}
