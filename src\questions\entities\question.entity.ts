import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { QuestionMedicalCondition } from './question-medical-condition.entity';
import { Transform } from 'class-transformer';
import { AssessmentQuestions } from 'src/assessments/entities/assessment-questions.entity';

@Entity('questions')
export class Question {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('longtext')
  question_text: string;

  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  @Column('json')
  options: any;

  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  @Column('json')
  rules: any;

  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  @Column('json')
  triggering_answers: any;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;

  @OneToMany(
    () => QuestionMedicalCondition,
    (questionMedicalCondition) => questionMedicalCondition.question,
  )
  questionMedicalConditions: QuestionMedicalCondition[];

  @OneToMany(
    () => AssessmentQuestions,
    (assessmentQuestion) => assessmentQuestion.question,
  )
  assessmentQuestions: AssessmentQuestions[];
}
