import { Child } from 'src/children/entities/child.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  JoinTable,
  ManyToMany,
} from 'typeorm';
import { AssessmentExercise } from './assessment-exersice.entity';
import { AssessmentQuestions } from './assessment-questions.entity';
import { AssessmentMedicalCondition } from './assessment-medical-condition.entity';
import { Transaction } from 'src/transations/entities/transation.entity';

export enum AssessmentStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

@Entity('assessments')
export class Assessment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Child, (child) => child.assessments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'child_id' })
  child: Child;

  @Column({ type: 'boolean', default: false })
  medical_questions_pass: boolean;

  @Column({ type: 'boolean', default: false })
  questionare_pass: boolean;

  @Column({ type: 'varchar' })
  child_id: string;

  @Column({ type: 'int' })
  basic_question_answered_count: number;

  @Column({
    type: 'enum',
    enum: AssessmentStatus,
    default: AssessmentStatus.IN_PROGRESS,
  })
  status: AssessmentStatus;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  word_tracing_exersice_score: number | null;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  image_tracing_exersice_score: number | null;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @OneToMany(
    () => AssessmentQuestions,
    (assessmentQuestions) => assessmentQuestions.assessment,
  )
  assessmentQuestions: AssessmentQuestions[];

  @ManyToMany(
    () => MedicalCondition,
    (medicalCondition) => medicalCondition.assessments,
    {
      cascade: true,
    },
  )
  @JoinTable({
    name: 'assessment_medical_condition',
    joinColumn: { name: 'assessment_id', referencedColumnName: 'id' },
    inverseJoinColumn: {
      name: 'medical_condition_id',
      referencedColumnName: 'id',
    },
  })
  medicalConditions: MedicalCondition[];

  @OneToMany(
    () => AssessmentExercise,
    (assessmentExercise) => assessmentExercise.assessment,
  )
  assessmentExercises: AssessmentExercise[];

  @OneToMany(
    () => AssessmentMedicalCondition,
    (assessmentMedicalCondition) => assessmentMedicalCondition.assessment,
  )
  assessmentMedicalConditions: AssessmentMedicalCondition[];

  @OneToMany(() => Transaction, (transaction) => transaction.plan)
  transactions: Transaction[];
}
