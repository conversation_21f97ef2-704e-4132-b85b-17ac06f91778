import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Subscription } from './entities/subscription.entity';
import { SubscriptionsController } from './subscriptions.controller';
import { SubscriptionsService } from './subscriptions.service';
import { CommonModule } from 'src/common/common.module';
import { UsersModule } from 'src/users/users.module';
import { ChildrenModule } from 'src/children/children.module';
import { PlanPricesModule } from 'src/plan-prices/plan-prices.module';
import { StripeModule } from 'src/stripe/stripe.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Subscription]),
    CommonModule,
    UsersModule,
    ChildrenModule,
    PlanPricesModule,
    forwardRef(() => StripeModule.forRootAsync()),
  ],
  controllers: [SubscriptionsController],
  providers: [SubscriptionsService],
  exports: [SubscriptionsService], // Export TypeOrmModule for use in related modules
})
export class SubscriptionsModule {}
