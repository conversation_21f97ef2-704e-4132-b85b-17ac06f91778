import {
  Controller,
  Post,
  Body,
  Param,
  NotFoundException,
  Req,
  UseGuards,
} from '@nestjs/common';
import { StripeService } from './stripe.service';
import { UsersService } from 'src/users/users.service';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Role } from 'src/common/decorators/roles.decorator';
import { PlansService } from 'src/plans/plans.service';
import { CreatePaymentIntentDto } from './dto/create-payment-indent.dto';
import { AssessmentsService } from 'src/assessments/assessments.service';
import { ResponseService } from 'src/common/services/response.service';

@Controller('stripe')
export class StripeController {
  constructor(
    private readonly stripeService: StripeService,
    private readonly usersService: UsersService,
    private readonly plansService: PlansService,
    private readonly assessmentService: AssessmentsService,
    private readonly resposneService: ResponseService,
  ) {}

  //@Post('create-checkout-session')
  async createCheckoutSession(
    @Body('reportId') reportId: string,
    @Body('priceId') priceId: string,
  ) {
    // const checkoutUrl = await this.stripeService.createCheckoutSession(
    //   priceId,
    // );
    // return { url: checkoutUrl };
  }

  @Post('verify-session/:sessionId')
  async verifySession(@Param('sessionId') sessionId: string) {
    const session = await this.stripeService.verifySession(sessionId);
    return session;
  }

  @Post('create-customer/:userId')
  async createCustomer(@Param('userId') user_id: string) {
    const client = await this.usersService.findOne(user_id);
    if (!client) {
      throw new NotFoundException('User Not Found');
    }
    const session = await this.stripeService.createCustomer(client);
    return session;
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('teacher', 'parent')
  @Post('create-checkout-session') //@Post('create-payment-intent')
  async createPaymentIntent(
    @Body() createPaymentIntentDto: CreatePaymentIntentDto,
    @Req() req,
  ) {
    const plan = await this.plansService.findOne(
      createPaymentIntentDto.plan_id,
    );
    if (!plan) {
      throw new NotFoundException('Plan Not found');
    }
    const assessment = await this.assessmentService.findOne(
      createPaymentIntentDto.assessment_id,
    );
    if (!assessment) {
      throw new NotFoundException('Assessment Not found');
    }
    const data = await this.stripeService.createCheckoutSession(
      req.user.sub,
      plan,
      assessment,
    );
    return this.resposneService.successResponse(
      'Payment Session Created Sucessfully',
      data,
    );
  }
}
