import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateSubscriptionsTable1746709292297
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'subscriptions',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'user_id',
            type: 'varchar',
            scale: 36,
            isNullable: true,
          },
          {
            name: 'child_id',
            type: 'varchar',
            scale: 36,
            isNullable: true,
          },
          {
            name: 'plan_id',
            type: 'varchar',
            scale: 36,
            isNullable: true,
          },
          {
            name: 'plan_price_id',
            type: 'varchar',
            scale: 36,
            isNullable: true,
          },
          {
            name: 'stripe_subscription_id',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'start_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'end_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'next_due_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'cancelled_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'cancellation_effective_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'enum',
            enum: [
              'created',
              'authenticated',
              'active',
              'pending',
              'halted',
              'cancelled',
              'completed',
              'expired',
              'past_due',
              'unpaid',
              'canceled',
              'incomplete',
              'incomplete_expired',
              'trialing',
              'paused',
            ],
            enumName: 'statusEnum',
            default: '"created"',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'now()',
          },
        ],
      }),
    );
    await queryRunner.query(
      'ALTER TABLE `subscriptions` ADD FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE RESTRICT',
    );
    await queryRunner.query(
      'ALTER TABLE `subscriptions` ADD FOREIGN KEY (plan_price_id) REFERENCES plan_prices(id) ON DELETE RESTRICT',
    );
    await queryRunner.query(
      'ALTER TABLE `subscriptions` ADD FOREIGN KEY (child_id) REFERENCES children(id) ON DELETE RESTRICT',
    );
    await queryRunner.query(
      'ALTER TABLE `subscriptions` ADD FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('subscriptions');
  }
}
