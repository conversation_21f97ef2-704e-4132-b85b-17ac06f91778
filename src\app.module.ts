import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';
import { UserOtpModule } from './user-otp/user-otp.module';
import { ChildrenModule } from './children/children.module';
import { ClassesModule } from './classes/classes.module';
import { QuestionsModule } from './questions/questions.module';
import { MedicalConditionModule } from './medical-condition/medical-condition.module';
import { AssessmentsModule } from './assessments/assessments.module';
import { ExerciseModule } from './exercise/exercise.module';
import { StripeModule } from './stripe/stripe.module';
import { PlansModule } from './plans/plans.module';
import { TransationsModule } from './transations/transations.module';
import { ReportModule } from './report/report.module';
import { ImageUploadModule } from './image-upload/image-upload.module';
import { QuestionOptionsModule } from './question-options/question-options.module';
import { ExerciseTypesModule } from './exercise-types/exercise-types.module';
import { PracticeExercisesModule } from './practice-exercises/practice-exercises.module';
import { OptionQuestionsModule } from './option-questions/option-questions.module';
import { DownloadableQuestionsModule } from './downloadable-questions/downloadable-questions.module';
import { HearAndSelectModule } from './hear-and-select/hear-and-select.module';
import { SpeedTypingModule } from './speed-typing/speed-typing.module';
import { MatchingGameModule } from './matching-game/matching-game.module';
import { CustomGamesModule } from './custom-games/custom-games.module';
import { ExerciseResultsModule } from './exercise-results/exercise-results.module';
import { PlanPricesModule } from './plan-prices/plan-prices.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({
        type: 'mysql',
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT) || 3306,
        username: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        autoLoadEntities: true,
      }),
    }),
    UsersModule,
    AuthModule,
    CommonModule,
    UserOtpModule,
    ChildrenModule,
    ClassesModule,
    QuestionsModule,
    MedicalConditionModule,
    AssessmentsModule,
    ExerciseModule,
    StripeModule.forRootAsync(),
    PlansModule,
    TransationsModule,
    ReportModule,
    ImageUploadModule,
    QuestionOptionsModule,
    ExerciseTypesModule,
    PracticeExercisesModule,
    OptionQuestionsModule,
    DownloadableQuestionsModule,
    HearAndSelectModule,
    SpeedTypingModule,
    MatchingGameModule,
    CustomGamesModule,
    ExerciseResultsModule,
    PlanPricesModule,
    SubscriptionsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
