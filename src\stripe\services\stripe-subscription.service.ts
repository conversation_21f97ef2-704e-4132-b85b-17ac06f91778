import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { TransactionsPerSecond } from 'aws-sdk/clients/personalize';
import { Child } from 'src/children/entities/child.entity';
import { PlanPrice } from 'src/plan-prices/entities/plan-price.entity';
import { Plan } from 'src/plans/entities/plan.entity';
import { CreateSubscriptionDto } from 'src/subscriptions/dto/create-subscription.dto';
import { SubscriptionStatus } from 'src/subscriptions/entities/subscription.entity';
import { SubscriptionsService } from 'src/subscriptions/subscriptions.service';
import {
  Transaction,
  TransactionStatus,
  TransactionType,
} from 'src/transations/entities/transation.entity';
import { TransationsService } from 'src/transations/transations.service';
import { User } from 'src/users/entities/user.entity';
import Stripe from 'stripe';

@Injectable()
export class StripeSubscriptionService {
  private stripe_secret_key: string;
  private stripe_success_url: string;
  private stripe_cancel_url: string;

  constructor(
    private readonly transactionsService: TransationsService,
    @Inject(forwardRef(() => SubscriptionsService))
    private readonly subscriptionsService: SubscriptionsService,
  ) {
    this.stripe_secret_key =
      process.env.STRIPE_SECRET_KEY ||
      'sk_test_51RMO9NRcfzG5fhn3LfKKqDZFtlIM6s0xFEaRtX1S7XhwILmK6W32SGztnW70xBBl5femi6ySqp9eVQYc25OSbBQC00ZeMFnhZe';
    this.stripe_success_url =
      process.env.STRIPE_SUCCESS_URL || 'https://example.com/success';
    this.stripe_cancel_url =
      process.env.STRIPE_CANCEL_URL || 'https://example.com/cancel';
  }
  async initiateInstance() {
    const Stripe = require('stripe');
    return new Stripe(this.stripe_secret_key);
  }

  async getSubscriptionFromStripe(subscription_id) {
    const stripe = await this.initiateInstance();
    const subscription = await stripe.subscriptions.retrieve(subscription_id);
    return subscription;
  }

  async createCustomer(child: Child, user: User) {
    const email = child?.billing_address?.email ?? user.email;
    const name = child.name;
    const phone = child?.billing_address?.phone_number ?? user.phone_number;

    const stripe = await this.initiateInstance();
    try {
      const customer = await stripe.customers.create({
        email,
        name,
        phone,
        address: {
          city: child.billing_address.city,
          state: child.billing_address.state,
          postal_code: child.billing_address.postal_code,
          line1: child.billing_address.line1,
          line2: child.billing_address.line2,
        },
        shipping: {
          name,
          phone,
          address: {
            city: child.billing_address.city,
            state: child.billing_address.state,
            postal_code: child.billing_address.postal_code,
            line1: child.billing_address.line1,
            line2: child.billing_address.line2,
          },
        },
      });
      child.stripe_customer_id = customer.id;
      return customer.id;
    } catch (ex) {
      console.error('Error creating customer in Stripe:', ex.message);
      throw new Error(`Error creating customer in Stripe : ${ex.message}`);
    }
  }
  async createSection(
    plan: Plan,
    plan_price: PlanPrice,
    child: Child,
    user: User,
    createSubscriptionDto: CreateSubscriptionDto,
  ) {
    //check if a customer created in stripe
    try {
      const price_id = plan_price.stripe_price_id;
      const customer = await this.getStripeCustomerIdOrCreateCustomer(
        child,
        user,
      );
      if (!customer) {
        return false;
      }
      const where = {
        child_id: child.id,
        user_id: user.id,
        plan_id: plan.id,
        plan_price_id: plan_price.id,
        status: 'pending',
      };
      const stripeSession =
        await this.transactionsService.findOneByParam(where);
      if (stripeSession) {
        const session = await this.getSessioObject(stripeSession.session_id);
        return session;
      }
      const stripe = await this.initiateInstance();

      const session = await stripe.checkout.sessions.create({
        mode: 'subscription',
        line_items: [
          {
            price: price_id,
            quantity: 1,
          },
        ],
        currency: 'GBP',
        customer: customer,
        allow_promotion_codes: true,
        success_url:
          createSubscriptionDto.success_url ?? this.stripe_success_url,
        cancel_url: createSubscriptionDto.cancel_url ?? this.stripe_cancel_url,
      });
      const stripeTransaction =
        await this.transactionsService.getStripeTransactionInstance();
      stripeTransaction.user_id = user.id;
      stripeTransaction.child_id = child.id;
      stripeTransaction.plan_id = plan.id;
      stripeTransaction.plan_price_id = plan_price.id;
      stripeTransaction.amount = plan_price.price;
      stripeTransaction.type = TransactionType.SUBSCRIPTION;

      stripeTransaction.session_id = session.id;
      await this.transactionsService.updateEntity(stripeTransaction);
      return session;
    } catch (ex) {
      console.log(ex.message, 'session create stripe');
      throw new Error(`Error creating session in Stripe : ${ex.message}`);
      return false;
    }
  }

  async getStripeCustomerIdOrCreateCustomer(child: Child, user: User) {
    let customer;
    if (child.stripe_customer_id) {
      customer = child.stripe_customer_id;
    } else {
      customer = this.createCustomer(child, user);
    }
    return customer;
  }

  async getSessioObject(session_id: string) {
    const stripe = await this.initiateInstance();

    const session = await stripe.checkout.sessions.retrieve(session_id);
    return session;
  }

  async createProduct(name: string) {
    const stripe = await this.initiateInstance();
    return await stripe.products.create({
      name: name,
    });
  }

  async createPrice(price: number, product_id: string) {
    const interval = 'month';

    const stripe = await this.initiateInstance();
    return await stripe.prices.create({
      unit_amount: await this.convertAmountToPaisa(price),
      currency: 'inr',
      recurring: { interval: interval },
      product: product_id,
    });
  }
  async convertAmountToPaisa(amount) {
    return amount * 100;
  }

  async createSubscription(stripeTransaction: Transaction, session: any) {
    const stripeSubscription = await this.getSubscriptionFromStripe(
      session.subscription,
    );
    const subscription = await this.subscriptionsService.initiateInstance();
    subscription.user_id = stripeTransaction.user_id;
    subscription.child_id = stripeTransaction.child_id;
    subscription.plan_id = stripeTransaction.plan_id;
    subscription.plan_price_id = stripeTransaction.plan_price_id;
    subscription.status = SubscriptionStatus.ACTIVE;
    subscription.stripe_subscription_id = stripeSubscription.id;
    subscription.start_date = new Date(stripeSubscription.start_date * 1000);
    subscription.next_due_date = new Date(
      stripeSubscription.current_period_end * 1000,
    );

    const subscription_created =
      await this.subscriptionsService.saveInstance(subscription);
    stripeTransaction.status = TransactionStatus.COMPLETED;
    stripeTransaction.subscription_id = subscription_created.id;
    await this.transactionsService.updateEntity(stripeTransaction);
  }

  async handleCheckoutSessionExpired(session: any) {
    const transaction = await this.transactionsService.findOneByParam({
      session_id: session.id,
    });

    if (transaction) {
      transaction.status = TransactionStatus.FAILED;
      await this.transactionsService.updateEntity(transaction);
      console.log('PaymentIntent marked as failed:', transaction);
    }
  }

  async handleSubscriptionUpdated(session: any) {
    const subscription = await this.subscriptionsService.findOneByParam({
      stripe_subscription_id: session.id,
    });
    if (!subscription) {
      return false;
    }
    const stripe_subscription = await this.getSubscriptionFromStripe(
      session.id,
    );
    subscription.next_due_date = new Date(
      stripe_subscription.current_period_end * 1000,
    );
    subscription.next_due_date = new Date(session.current_period_end * 1000);
    if (session.cancel_at !== null) {
      subscription.cancellation_effective_date = new Date(
        session.cancel_at * 1000,
      );
    }
    subscription.status = stripe_subscription.status;
    const updated_subscription =
      await this.subscriptionsService.saveInstance(subscription);
  }

  async handleInvoicePaid(session: any) {
    const subscription_id = session.subscription;
    const subscription = await this.subscriptionsService.findOneByParam({
      stripe_subscription_id: subscription_id,
    });
    if (!subscription) {
      return false;
    }
    const stripe_subscription =
      await this.getSubscriptionFromStripe(subscription_id);
    subscription.next_due_date = new Date(
      stripe_subscription.current_period_end * 1000,
    );
    subscription.status = stripe_subscription.status;
    await this.subscriptionsService.saveInstance(subscription);
  }

  async handleInvoicePaymentFailed(session: any) {
    const stripe_subscription_id = session.subscription;
    const subscription = await this.subscriptionsService.findOneByParam({
      stripe_subscription_id: stripe_subscription_id,
    });
    if (!subscription) {
      return false;
    }
    const stripe_subscription = await this.getSubscriptionFromStripe(
      stripe_subscription_id,
    );
    subscription.next_due_date = new Date(
      stripe_subscription.current_period_end * 1000,
    );
    subscription.next_due_date = new Date(session.current_period_end * 1000);
    subscription.status = stripe_subscription.status;
    await this.subscriptionsService.saveInstance(subscription);
  }
}
