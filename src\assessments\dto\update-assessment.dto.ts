import { PartialType } from '@nestjs/mapped-types';
import { CreateAssessmentDto } from './create-assessment.dto';
import { IsNotEmpty, IsNumber, IsString, Max, Min } from 'class-validator';

export class UpdateAssessmentDto extends PartialType(CreateAssessmentDto) {}

export class UploadAssessmentExerciseScoreDto {
  @IsString()
  @IsNotEmpty()
  exercise_id: string;

  @IsNumber()
  @IsNotEmpty()
  @Min(0)
  @Max(100)
  score: number;
}
