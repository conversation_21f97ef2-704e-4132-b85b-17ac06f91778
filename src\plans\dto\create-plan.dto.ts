import {
  IsString,
  IsNotEmpty,
  IsEnum,
  IsDecimal,
  IsOptional,
  IsNumber,
  Min,
} from 'class-validator';
import { PlanType } from '../entities/plan.entity';

export class CreatePlanDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEnum(PlanType)
  @IsNotEmpty()
  type: PlanType;

  @IsNumber({}, { message: 'amount must be a valid number' })
  @Min(0.01, { message: 'amount must be at least 0.01' })
  @IsNotEmpty()
  amount: number;

  @IsNumber({}, { message: 'amount must be a valid number' })
  @Min(0.01, { message: 'amount must be at least 0.01' })
  @IsNotEmpty()
  discount_amount: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  thumbnail?: string;

  @IsString()
  @IsOptional()
  stripe_plan_id?: string;

  @IsOptional()
  @IsNotEmpty()
  is_published?: boolean;
  
}
