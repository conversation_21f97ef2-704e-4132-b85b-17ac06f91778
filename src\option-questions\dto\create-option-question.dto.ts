import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateOptionQuestionDto {
  @IsString()
  exercise_type_id: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  question_image: string;

  @IsString()
  option_1_image: string;

  @IsString()
  option_2_image: string;

  @IsString()
  option_3_image: string;

  @IsString()
  option_4_image: string;

  @IsOptional()
  @IsString()
  option_5_image?: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsInt({ each: true })
  @IsIn([1, 2, 3, 4, 5], { each: true })
  correct_options: number[];

  @IsInt()
  time_limit: number;

  @IsOptional()
  @IsString()
  answer_description?: string;

  @IsOptional()
  @IsNotEmpty()
  level: number;

  @IsOptional()
  @IsString()
  text?: string;

  @IsOptional()
  @IsString()
  thumbnail?: string;

  @IsArray()
  @IsNotEmpty()
  ages: number[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Type(() => String)
  medical_condition_ids?: string[];
}
