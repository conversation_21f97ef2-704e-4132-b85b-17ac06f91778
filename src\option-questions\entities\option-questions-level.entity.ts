import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  JoinColumn,
  OneToOne,
} from 'typeorm';
import { OptionQuestion } from './option-question.entity';
import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';

@Entity('option_questions_levels')
export class OptionQuestionLevel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => OptionQuestion, (optionQuestion) => optionQuestion.level)
  optionQuestion: OptionQuestion;

  @Column({ name: 'exersise_type_id', type: 'varchar' })
  exersise_type_id: string;

  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.optionQuestionLevels,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'exersise_type_id' })
  exerciseType: ExerciseType;

  @Column({ name: 'level', type: 'int' })
  level: number;
}
