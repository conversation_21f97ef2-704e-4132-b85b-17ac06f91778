import { Class } from 'src/classes/entities/class.entity';
import { Gender } from '../entities/child.entity';
import { User } from 'src/users/entities/user.entity';

export interface CreateChildInterface {
  name: string;
  age: number;
  date_of_birth: string;
  gender: Gender;
  country?: string;
  class: Class;
  user_id: string;
  profile_image?: string;
  hand_preference?: string;
  acadamic_performance?: string;
  language?: string;
  user?: User;
  billing_address?: BillingAddressChild;
}

export interface BillingAddressChild {
  email: string;
  phone_number: string;
  city: string;
  state: string;
  postal_code: string;
  line1: string;
  line2: string;
}
