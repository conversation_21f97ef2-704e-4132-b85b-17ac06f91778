import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateAssessmentMedicalConditionTable1728904774440
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'exercise_medical_condition',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid',
          },
          {
            name: 'exercise_id',
            type: 'varchar',
            length: '36',
          },
          {
            name: 'medical_condition_id',
            type: 'varchar',
            length: '36',
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      'exercise_medical_condition',
      new TableForeignKey({
        columnNames: ['exercise_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'exercise',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'exercise_medical_condition',
      new TableForeignKey({
        columnNames: ['medical_condition_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'medical_condition',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('exercise_medical_condition');
  }
}
