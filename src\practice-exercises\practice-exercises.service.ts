import { Injectable } from '@nestjs/common';
import { CreatePracticeExerciseDto } from './dto/create-practice-exercise.dto';
import { UpdatePracticeExerciseDto } from './dto/update-practice-exercise.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, In, Repository } from 'typeorm';
import { PracticeExerciseAge } from './entities/practice-exercise-age.entity';
import { PracticeExercise } from './entities/practice-exercise.entity';
import { CreatePracticeExerciseInterface } from './interface/create-practice-exercise.interface';
import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { PracticeExercisesMedicalConditions } from './entities/practice-exersise-medical-conditions.entity';
import { PracticeExerciseLevel } from './entities/practice-exercise-level.entity';

@Injectable()
export class PracticeExercisesService {
  constructor(
    @InjectRepository(PracticeExercise)
    private practiceExerciseRepository: Repository<PracticeExercise>,
    @InjectRepository(PracticeExerciseLevel)
    private practiceExercisesLevelsRepository: Repository<PracticeExerciseLevel>,
    @InjectRepository(PracticeExerciseAge)
    private practiceExerciseAgeRepository: Repository<PracticeExerciseAge>,
    @InjectRepository(PracticeExercisesMedicalConditions)
    private practiceExercisesMedicalConditionsRepo: Repository<PracticeExercisesMedicalConditions>,
  ) {}

  async create(
    createPracticeExerciseInterface: CreatePracticeExerciseInterface,
    ages: number[],
    medical_conditions?: MedicalCondition[],
  ) {
    const newPracticeExercise = this.practiceExerciseRepository.create({
      ...createPracticeExerciseInterface,
    });
    const savedExersise =
      await this.practiceExerciseRepository.save(newPracticeExercise);
    const ageEntities = ages.map((age) => {
      const ageEntity = new PracticeExerciseAge();
      ageEntity.age = age;
      ageEntity.practice_exercise_id = newPracticeExercise.id;
      return ageEntity;
    });
    await this.practiceExerciseAgeRepository.save(ageEntities);

    //remove medical conditions
    // if (medical_conditions && medical_conditions.length != 0) {
    //   const exeresieMEdicalConditionEnitites = medical_conditions.map(
    //     (medicalCondition) => {
    //       const ExercisesMedicalConditionsEntity =
    //         new PracticeExercisesMedicalConditions();
    //       ExercisesMedicalConditionsEntity.medical_condition_id =
    //         medicalCondition.id;
    //       ExercisesMedicalConditionsEntity.practice_exersise_id =
    //         newPracticeExercise.id;
    //       return ExercisesMedicalConditionsEntity;
    //     },
    //   );
    //   await this.practiceExercisesMedicalConditionsRepo.save(
    //     exeresieMEdicalConditionEnitites,
    //   );
    // }
    return savedExersise;
  }

  async findAll(filter: any) {
    const page = parseInt(filter.page) || 1; // Default to page 1
    const limit = parseInt(filter.limit) || 10; // Default to 10 items per page
    const skip = (page - 1) * limit;
    let where: FindManyOptions['where'] = {};

    if (filter.time_limit) {
      where = { time_limit: filter.time_limit };
    }

    if (filter.trigger_score) {
      where = { ...where, trigger_score: filter.trigger_score };
    }

    if (filter.ages) {
      where = { ...where, ages: { age: In(filter.ages) } };
    }

    if (filter.exercise_type_id) {
      where = {
        ...where,
        practice_exersise_type_id: filter.exercise_type_id,
      };
    }
    if (filter.exercise_type_identifier) {
      where = {
        ...where,
        exerciseType: { identifier: filter.exercise_type_identifier },
      };
    }
    const [exerciseList, total] =
      await this.practiceExerciseRepository.findAndCount({
        where,
        relations: ['exerciseType'],
        skip,
        take: limit,
      });
    return [exerciseList, total];
  }

  async findByParam(params: FindManyOptions['where'], relations?: string[]) {
    return this.practiceExerciseRepository.find({
      where: params,
      order: { level: { level: 'ASC' } },
      relations,
    });
  }

  async findOne(id: string) {
    return await this.practiceExerciseRepository.findOne({
      where: { id },
      relations: ['exerciseType', 'ages'],
    });
  }

  update(id: string, updatePracticeExerciseDto: UpdatePracticeExerciseDto) {
    return `This action updates a #${id} practiceExercise`;
  }

  async remove(id: string) {
    return await this.practiceExerciseRepository.delete(id);
  }

  // async checkAndUpdateRankList(
  //   level: number,
  //   practiceExerciseType: ExerciseType,
  //   practiceExercise: PracticeExercise,
  // ) {
  //   const levelExist = await this.practiceExercisesLevelsRepository.findOne({
  //     where: { level, practiceExerciseType },
  //     relations: ['practiceExercise'],
  //   });

  //   let savedlevel;

  //   if (levelExist) {
  //     if (levelExist.practiceExercise) {
  //       const existingLevels =
  //         await this.practiceExercisesLevelsRepository.find({
  //           where: { practiceExerciseType },
  //           select: ['level'],
  //           order: { level: 'ASC' },
  //         });
  //       const levelsArray = existingLevels.map((levelObj) => levelObj.level);
  //       const missingLevels = [];
  //       for (let i = 1; i < levelsArray.length; i++) {
  //         if (levelsArray[i] !== levelsArray[i - 1] + 1) {
  //           for (let j = levelsArray[i - 1] + 1; j < levelsArray[i]; j++) {
  //             missingLevels.push(j);
  //           }
  //         }
  //       }

  //       // if (missingLevels.length > 0) {
  //       //   throw new Error('There are missing levels in the sequence');
  //       // }
  //       await this.practiceExerciseRepository
  //         .createQueryBuilder()
  //         .update(PracticeExerciseLevel)
  //         .set({ level: () => 'level + 1' })
  //         .where('practice_exercise_type_id = :exerciseTypeId', {
  //           exerciseTypeId: practiceExerciseType.id,
  //         })
  //         .andWhere('level >= :level', { level })
  //         .execute();
  //       const newLevel = this.practiceExercisesLevelsRepository.create({
  //         practiceExerciseType,
  //         level,
  //       });
  //       savedlevel =
  //         await this.practiceExercisesLevelsRepository.save(newLevel);
  //     } else {
  //       savedlevel = levelExist;
  //     }
  //   } else {
  //     const newLevel = this.practiceExercisesLevelsRepository.create({
  //       practiceExerciseType,
  //       level,
  //     });
  //     savedlevel = await this.practiceExercisesLevelsRepository.save(newLevel);
  //   }

  //   practiceExercise.level = savedlevel;
  //   await this.practiceExerciseRepository.save(practiceExercise);
  // }

  async updateAge(practice_exercise: PracticeExercise, ages: number[]) {
    await this.practiceExerciseAgeRepository.delete({
      practice_exercise_id: practice_exercise.id,
    });

    const ageEntities = ages.map((age) => {
      const ageEntity = new PracticeExerciseAge();
      ageEntity.age = age;
      ageEntity.practice_exercise_id = practice_exercise.id;
      return ageEntity;
    });
    return await this.practiceExerciseAgeRepository.save(ageEntities);
  }

  // async updateMedicalConditions(
  //   practice_exercise: PracticeExercise,
  //   medical_conditions: MedicalCondition[],
  // ) {
  //   await this.practiceExercisesMedicalConditionsRepo.delete({
  //     practice_exersise_id: practice_exercise.id,
  //   });
  //   const exerciseMedicalConditionEntities = medical_conditions.map(
  //     (medicalCondition) => {
  //       const entity = new PracticeExercisesMedicalConditions();
  //       entity.medical_condition_id = medicalCondition.id;
  //       entity.practice_exersise_id = practice_exercise.id;
  //       return entity;
  //     },
  //   );
  //   await this.practiceExercisesMedicalConditionsRepo.save(
  //     exerciseMedicalConditionEntities,
  //   );
  // }

  async saveEntity(practice_exercise: PracticeExercise) {
    return await this.practiceExerciseRepository.save(practice_exercise);
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[PracticeExercise[], number]> {
    const [questionTypes, total] =
      await this.practiceExerciseRepository.findAndCount({
        where: params,
        relations,
        skip,
        take,
      });
    return [questionTypes, total];
  }
}
