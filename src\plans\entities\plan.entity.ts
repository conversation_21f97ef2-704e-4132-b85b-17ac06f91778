import { Transaction } from 'src/transations/entities/transation.entity';
import { PlanPrice } from '../../plan-prices/entities/plan-price.entity';
import { Subscription } from '../../subscriptions/entities/subscription.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

export enum PlanType {
  SUMMARY_REPORT = 'SUMMARY_REPORT',
  DETAILED_REPORT = 'DETAILED_REPORT',
  SUBSCRIPTION = 'SUBSCRIPTION',
}

@Entity('plans')
export class Plan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  stripe_plan_id: string;

  @Column({ type: 'boolean', default: true })
  is_published: boolean;

  @Column({
    type: 'enum',
    enum: PlanType,
  })
  type: PlanType;

  @Column('decimal', { precision: 10, scale: 2 })
  amount: number;

  @Column('decimal', { precision: 10, scale: 2 })
  discount_amount: number;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;

  @OneToMany(() => Transaction, (transaction) => transaction.plan)
  transactions: Transaction[];

  @OneToMany(() => PlanPrice, (planPrice) => planPrice.plan, {
    cascade: true,
  })
  planPrices: PlanPrice[];

  @OneToMany(() => Subscription, (subscription) => subscription.plan, {
    cascade: true,
  })
  subscriptions: Subscription[];
}
