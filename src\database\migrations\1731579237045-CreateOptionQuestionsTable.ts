import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateOptionQuestionsTable1731579237045
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'option_questions',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'exercise_type_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'question_image',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'option_1_image',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'option_2_image',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'option_3_image',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'option_4_image',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'option_5_image',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'correct_options',
            type: 'json',
            isNullable: false,
          },
          {
            name: 'time_limit',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'answer_description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['exercise_type_id'],
            referencedTableName: 'exercise_types',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('option_questions');
  }
}
