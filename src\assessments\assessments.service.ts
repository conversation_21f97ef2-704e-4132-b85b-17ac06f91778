import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateAssessmentDto } from './dto/create-assessment.dto';
import { UpdateAssessmentDto } from './dto/update-assessment.dto';
import { Assessment, AssessmentStatus } from './entities/assessment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { AssessmentExercise } from './entities/assessment-exersice.entity';
import { AssessmentMedicalCondition } from './entities/assessment-medical-condition.entity';
import { Child } from 'src/children/entities/child.entity';
import { QuestionsService } from 'src/questions/questions.service';
import { AssessmentQuestions } from './entities/assessment-questions.entity';
import { Question } from 'src/questions/entities/question.entity';
import { AssessmentAnswersInterface } from './interface/assessment-answers.interface';
import internal from 'stream';
import { User, UserType } from 'src/users/entities/user.entity';
import { throwError } from 'rxjs';

@Injectable()
export class AssessmentsService {
  constructor(
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>,
    @InjectRepository(AssessmentExercise)
    private readonly assessmentExerciseRepository: Repository<AssessmentExercise>,
    @InjectRepository(AssessmentMedicalCondition)
    private readonly assessmentMedicalRepository: Repository<AssessmentMedicalCondition>,
    @InjectRepository(AssessmentQuestions)
    private readonly assessmentQuestionsRepository: Repository<AssessmentQuestions>,
    private readonly questionService: QuestionsService,
  ) {}

  async startAssessment(child: Child) {
    let assessment = await this.assessmentRepository.findOne({
      where: { child_id: child.id, status: AssessmentStatus.IN_PROGRESS },
      relations: ['assessmentQuestions'],
    });
    let isNewAssessment = false;
    if (!assessment) {
      const newAssessment = this.assessmentRepository.create({
        child,
        status: AssessmentStatus.IN_PROGRESS,
      });
      assessment = await this.assessmentRepository.save(newAssessment);
      isNewAssessment = true;
    }
    const questions = await this.questionService.findAllMedicalQuestions();
    const assessmentQuestions: AssessmentQuestions[] = [];
    const returnAssessmentQuestions: AssessmentQuestions[] = [];

    for (const question of questions) {
      if (
        question.rules.max_age > child.age &&
        question.rules.min_age < child.age
      ) {
        const assessmentQuestion = new AssessmentQuestions();
        assessmentQuestion.assessment_id = assessment.id;
        assessmentQuestion.question = question;
        const { rules, created_at, updated_at, ...filteredQuestion } = question;
        assessmentQuestion.question = filteredQuestion as Question;
        assessmentQuestions.push(assessmentQuestion);
        delete assessmentQuestion.assessment;
        returnAssessmentQuestions.push(assessmentQuestion);
      }
    }
    if (isNewAssessment == true) {
      await this.assessmentQuestionsRepository.save(assessmentQuestions);
    }

    return returnAssessmentQuestions;
  }

  async continueAssessment(child: Child) {
    const assessment = await this.assessmentRepository.findOne({
      where: { child_id: child.id, status: AssessmentStatus.IN_PROGRESS },
      relations: ['assessmentQuestions.question'],
    });
    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }
    const medicalQuestions =
      await this.questionService.findAllMedicalQuestions();
    const incompleteMedicalQuestion = assessment.assessmentQuestions.some(
      (assessmentQuestion) =>
        assessmentQuestion.question.rules.medical_question === true &&
        (!assessmentQuestion.answer || assessmentQuestion.answer.trim() === ''),
    );
    if (incompleteMedicalQuestion) {
      throw new ConflictException(
        'This assessment failed or not completed in the medical question round. Cannot continue further',
      );
    }
    if (assessment.medical_questions_pass == false) {
      assessment.medical_questions_pass = true;
      await this.assessmentRepository.save(assessment);
    }
    const questions = await this.questionService.findAll();
    const assessmentQuestions: AssessmentQuestions[] = [];
    const returnAssessmentQuestions: AssessmentQuestions[] = [];

    for (const question of questions) {
      if (
        question.rules.max_age > child.age &&
        question.rules.min_age < child.age &&
        question.rules.medical_question == false
      ) {
        const assessmentQuestion = new AssessmentQuestions();
        assessmentQuestion.assessment_id = assessment.id;
        assessmentQuestion.question = question;
        const { rules, created_at, updated_at, ...filteredQuestion } = question;
        assessmentQuestion.question = filteredQuestion as Question;
        assessmentQuestions.push(assessmentQuestion);
        delete assessmentQuestion.assessment;
        returnAssessmentQuestions.push(assessmentQuestion);
      }
    }
    if (assessment.assessmentQuestions.length === medicalQuestions.length) {
      await this.assessmentQuestionsRepository.save(assessmentQuestions);
    }

    return returnAssessmentQuestions;
  }

  async uploadAssessmentAnswers(
    assessment: Assessment,
    assessment_answers: AssessmentAnswersInterface[],
  ) {
    const assessmentQuestions = assessment.assessmentQuestions;
    let success_question_uploads = 0;
    let failed_question_uploads = 0;
    let assessment_status = 'pending';
    const errorDetails: string[] = [];
    for (const assessment_answer of assessment_answers) {
      const assessmentQuestion = assessmentQuestions.find(
        (question) => question.question_id === assessment_answer.question_id,
      );

      if (assessmentQuestion) {
        if (
          assessmentQuestion.question.options.includes(assessment_answer.answer)
        ) {
          assessmentQuestion.answer = assessment_answer.answer;
          try {
            await this.assessmentQuestionsRepository.save(assessmentQuestion);
            success_question_uploads++;
          } catch (error) {
            failed_question_uploads++;
            const errorMessage = `Failed to save question ID ${assessmentQuestion.question_id}: ${error.message}`;
            errorDetails.push(errorMessage);
            console.error(errorMessage, error);
          }
        } else {
          const validationError = `No option ${assessment_answer.answer} in question ID ${assessmentQuestion.question_id}`;
          errorDetails.push(validationError);
          throw new BadRequestException(validationError);
        }
      } else {
        failed_question_uploads++;
        const missingQuestionError = `Question ID ${assessment_answer.question_id} not found in the assessment.`;
        errorDetails.push(missingQuestionError);
      }
    }
    const nonMedicalQuestions = assessmentQuestions.filter(
      (assessment_question) =>
        assessment_question.question.rules &&
        assessment_question.question.rules.medical_question === false,
    );
    if (nonMedicalQuestions.length !== 0) {
      const allNonMedicalQuestionsAnswered = nonMedicalQuestions.every(
        (question) => question.answer !== null && question.answer !== undefined,
      );

      if (allNonMedicalQuestionsAnswered) {
        const questionIds = assessment.assessmentQuestions.map(
          (assessmentQuestion) => {
            return assessmentQuestion.question_id;
          },
        );
        const Questions = await this.questionService.findByParam(
          {
            id: In(questionIds),
          },
          [
            'questionMedicalConditions',
            'questionMedicalConditions.medical_condition',
          ],
        );

        const newAssessmentMedicalConditions: AssessmentMedicalCondition[] = [];

        for (const assessmentQuestion of assessment.assessmentQuestions) {
          const question = Questions.find(
            (q) => q.id === assessmentQuestion.question_id,
          );

          if (question) {
            if (
              question.triggering_answers &&
              question.triggering_answers.includes(assessmentQuestion.answer)
            ) {
              question.questionMedicalConditions.forEach(
                (questionMedicalCondition) => {
                  const newCondition = new AssessmentMedicalCondition();
                  newCondition.assessment_id = assessment.id;
                  newCondition.medical_condition_id =
                    questionMedicalCondition.medical_condition.id;
                  newCondition.assessment = assessment;
                  newCondition.medicalCondition =
                    questionMedicalCondition.medical_condition;

                  newAssessmentMedicalConditions.push(newCondition);
                },
              );
            }
          }
        }
        if (newAssessmentMedicalConditions.length > 0) {
          await this.assessmentMedicalRepository.save(
            newAssessmentMedicalConditions,
          );
        }
        assessment_status = 'Finished';
        assessment.questionare_pass = true;
        await this.assessmentRepository.save(assessment);
      }
    }
    return {
      assessment_status,
      success_question_uploads,
      failed_question_uploads,
      errorDetails,
    };
  }

  async saveAssessmentMedicalConditons(
    assessmentMedicalCondition: AssessmentMedicalCondition[],
  ) {
    return await this.assessmentMedicalRepository.save(
      assessmentMedicalCondition,
    );
  }

  async findAll(filter: any, user: User) {
    let where;
    if (user.user_type !== UserType.ADMIN) {
      const child_ids = user.children.map((child) => child.id);
      if (filter.child_id && child_ids.includes(filter.child_id)) {
        where = { child_id: filter.child_id };
      } else {
        where = { child_id: In(child_ids) };
      }
    } else {
      where = { child_id: filter.child_id };
    }
    return await this.assessmentRepository.find({ where });
  }

  async findOne(id: string) {
    return await this.assessmentRepository.findOne({
      where: { id },
      relations: ['assessmentMedicalConditions.medicalCondition'],
    });
  }

  async findOneByParam(params: { [key: string]: any }, relations?: string[]) {
    // Find and return the record based on dynamic parameters
    // return this.userRepository.findOneBy(params);
    return this.assessmentRepository.findOne({
      where: params,
      relations,
    });
  }

  update(id: number, updateAssessmentDto: UpdateAssessmentDto) {
    return `This action updates a #${id} assessment`;
  }

  remove(id: number) {
    return `This action removes a #${id} assessment`;
  }

  async saveInstance(assessment: Assessment) {
    return await this.assessmentRepository.save(assessment);
  }

  async checkAssessmentStatus(child_id: string) {
    const assessment = await this.assessmentRepository.findOne({
      where: { child_id },
      relations: ['assessmentQuestions.question'],
    });

    let continueAssessment = true;
    let medicalQuestionStatus: string = 'No Assessment Done';
    let questionareStatus: string = 'No Assessment Done';
    let wordTracingExerciseStatus = 'Not Done';
    let imageTracingExerciseStatus = 'Not Done';
    if (assessment) {
      if (assessment.medical_questions_pass === false) {
        const noFailedQuestions = await this.checkAllMedicalQuestionAnswered(
          assessment.assessmentQuestions,
        );
        if (noFailedQuestions == false) {
          medicalQuestionStatus = 'Pending';
        } else {
          medicalQuestionStatus = 'Passed';
        }
      } else {
        medicalQuestionStatus = 'Passed';
      }
      if (assessment.questionare_pass === false) {
        const status = this.validateQuestionare(assessment.assessmentQuestions);
        questionareStatus = status;
      } else {
        questionareStatus = 'Completed';
        continueAssessment = false;
      }
      if (assessment.word_tracing_exersice_score !== null) {
        wordTracingExerciseStatus = 'Completed';
      }
      if (assessment.image_tracing_exersice_score !== null) {
        imageTracingExerciseStatus = 'Completed';
      }
    }
    return {
      assessment_id: assessment?.id,
      continueAssessment,
      medicalQuestionStatus,
      questionareStatus,
      wordTracingExerciseStatus,
      imageTracingExerciseStatus,
    };
  }

  validateQuestionare(assessment_questions: AssessmentQuestions[]) {
    let nonMedicalQuestions: AssessmentQuestions[] = [];
    assessment_questions.map((assessment_question) => {
      if (assessment_question.question.rules.medical_question === false) {
        nonMedicalQuestions.push(assessment_question);
      }
    });
    if (nonMedicalQuestions.length == 0) {
      return 'Not Attempted';
    } else {
      return 'Pending';
    }
  }

  validateMedicalQuestions(assessment_questions: AssessmentQuestions[]) {
    if (assessment_questions.length > 0) {
      for (const assessment_question of assessment_questions) {
        if (assessment_question.question.rules.medical_question === true) {
          if (
            assessment_question.question.triggering_answers &&
            assessment_question.question.triggering_answers.includes(
              assessment_question.answer,
            )
          ) {
            return false;
          }
        }
      }
    }
    return true;
  }

  async checkAllMedicalQuestionAnswered(
    assessment_questions: AssessmentQuestions[],
  ) {
    const medical_questions =
      await this.questionService.findAllMedicalQuestions();
    const answeredMedicalQuestions = assessment_questions
      .filter(
        (assessment_question) =>
          assessment_question.answer !== null &&
          assessment_question.question.rules.medical_question === true,
      )
      .map((assessment_question) => assessment_question.question.id);

    const allAnswered = medical_questions.every((medical_question) =>
      answeredMedicalQuestions.includes(medical_question.id),
    );
    const answerStatus = this.validateMedicalQuestions(assessment_questions);
    if (answerStatus === false) {
      throw new ConflictException(
        'Some medical question is failed cannot proceed',
      );
    }
    return allAnswered;
  }

  async getFormattedReportData(assessmentId: string) {
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
      relations: [
        'child.class',
        'assessmentMedicalConditions.medicalCondition',
      ],
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    if (assessment.status !== AssessmentStatus.COMPLETED) {
      throw new ConflictException('Assessment is not completed');
    }
    const reportData = {
      testDate: new Date(assessment.created_at).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      }),
      name: assessment.child.name,
      dob: new Date(assessment.child.date_of_birth).toLocaleDateString(
        'en-GB',
        {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
        },
      ),
      age: assessment.child.age,
      gender: assessment.child.gender,
      schoolYear: assessment.child.class.class_name,
      language: assessment.child.language || 'Not Provided',
      country: assessment.child.country,
      tracingScore: assessment.word_tracing_exersice_score
        ? assessment.word_tracing_exersice_score
        : 0,
      imageTracingScore: assessment.image_tracing_exersice_score
        ? assessment.image_tracing_exersice_score
        : 0,
      areasOfConcern: Array.from(
        new Set(
          assessment.assessmentMedicalConditions.map(
            (condition) => condition.medicalCondition.name,
          ),
        ),
      ),
    };
    return { reportData, assessment };
  }
}
