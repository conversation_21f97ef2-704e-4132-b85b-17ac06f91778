import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddThumbnailfeildtoExercise1733204581121
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'practice_exercises',
      new TableColumn({ name: 'thumbnail', type: 'varchar', isNullable: true }),
    );

    await queryRunner.addColumn(
      'option_questions',
      new TableColumn({ name: 'thumbnail', type: 'varchar', isNullable: true }),
    );

    await queryRunner.addColumn(
      'downloadable_questions',
      new TableColumn({ name: 'thumbnail', type: 'varchar', isNullable: true }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('practice_exercises', 'thumbnail');
    await queryRunner.dropColumn('option_questions', 'thumbnail');
    await queryRunner.dropColumn('downloadable_questions', 'thumbnail');
  }
}
