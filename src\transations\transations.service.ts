import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateTransationDto } from './dto/create-transation.dto';
import { UpdateTransationDto } from './dto/update-transation.dto';
import { Transaction, TransactionStatus } from './entities/transation.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from 'src/users/entities/user.entity';
import { Assessment } from 'src/assessments/entities/assessment.entity';
import { PlansService } from 'src/plans/plans.service';
import { PlanType } from 'src/plans/entities/plan.entity';
import { NotFoundError } from 'rxjs';

@Injectable()
export class TransationsService {
  constructor(
    @InjectRepository(Transaction)
    private transationRepository: Repository<Transaction>,
    private readonly plansService: PlansService,
  ) {}
  async create(createTransationDto: CreateTransationDto, user: User) {
    const transaction = await this.transationRepository.create();
  }

  findAll() {
    return `This action returns all transations`;
  }

  async findWithRelations(
    params: { [key: string]: any },
    relations?: string[],
  ) {
    // Find and return the record based on dynamic parameters
    return await this.transationRepository.find({
      where: params,
      relations,
    });
  }

  findOne(id: number) {
    return `This action returns a #${id} transation`;
  }

  async findOneByParam(params: { [key: string]: any }, relations?: string[]) {
    // Find and return the record based on dynamic parameters
    // return this.userRepository.findOneBy(params);
    return await this.transationRepository.findOne({
      where: params,
      order: { created_at: 'DESC' },
      relations,
    });
  }

  update(id: number, updateTransationDto: UpdateTransationDto) {
    return `This action updates a #${id} transation`;
  }

  remove(id: number) {
    return `This action removes a #${id} transation`;
  }

  async getStripeTransactionInstance() {
    return new Transaction();
  }

  async updateEntity(transaction: Transaction) {
    return await this.transationRepository.save(transaction);
  }

  async checkTransation(
    assessment: Assessment,
    report_type: 'summary' | 'detailed',
  ) {
    let transation;
    if (report_type === 'summary') {
      transation = await this.findOneByParam({
        assessment_id: assessment.id,
        plan: { type: PlanType.SUMMARY_REPORT },
      });
    } else if (report_type === 'detailed') {
      transation = await this.findOneByParam({
        plan: { type: PlanType.DETAILED_REPORT },
      });
    } else {
      throw new NotFoundException('Invalid report type');
    }
    if (!transation) {
      throw new NotFoundException(
        'Transation not found, Make sure you selected the correct type or initiated a transation',
      );
    }
    if (transation.status == TransactionStatus.FAILED) {
      throw new ConflictException('Failed transation cannot procced');
    }
    if (transation.status == TransactionStatus.PENDING) {
      throw new ConflictException('Payment is not done');
    }
    return transation;
  }
}
