import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { ResponseService } from 'src/common/services/response.service';
import { OtpService } from 'src/otp/otp.service';
import { OtpModule } from 'src/otp/otp.module';
import { UserOtpModule } from 'src/user-otp/user-otp.module';
import { EngagespotModule } from 'src/engagespot/engagespot.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    OtpModule,
    UserOtpModule,
    EngagespotModule,
  ],
  controllers: [UsersController],
  providers: [UsersService, ResponseService],
  exports: [UsersService],
})
export class UsersModule {}
