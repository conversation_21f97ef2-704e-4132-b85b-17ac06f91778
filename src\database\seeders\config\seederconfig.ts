import { seeder } from 'nestjs-seeder';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { User } from 'src/users/entities/user.entity';
import { SuperAdminSeeder } from '../superadminseeder.seeder';
import { UserOtp } from 'src/user-otp/entities/user-otp.entity';
import { Child } from 'src/children/entities/child.entity';
import { Class } from 'src/classes/entities/class.entity';
import { Assessment } from 'src/assessments/entities/assessment.entity';
import { AssessmentExercise } from 'src/assessments/entities/assessment-exersice.entity';
import { AssessmentMedicalCondition } from 'src/assessments/entities/assessment-medical-condition.entity';
import { AssessmentQuestions } from 'src/assessments/entities/assessment-questions.entity';
import { Exercise } from 'src/exercise/entities/exercise.entity';
import { ExerciseMedicalCondition } from 'src/exercise/entities/exersice-medical-condition.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { QuestionMedicalCondition } from 'src/questions/entities/question-medical-condition.entity';
import { Question } from 'src/questions/entities/question.entity';
import { Transaction } from 'src/transations/entities/transation.entity';
import { Plan } from 'src/plans/entities/plan.entity';
import { PracticeExercise } from 'src/practice-exercises/entities/practice-exercise.entity';

seeder({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({
        type: 'mysql',
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT) || 3306,
        username: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        autoLoadEntities: true,
      }),
    }),
    TypeOrmModule.forFeature([
      User,
      UserOtp,
      Child,
      Class,
      Assessment,
      AssessmentExercise,
      AssessmentMedicalCondition,
      AssessmentQuestions,
      Exercise,
      ExerciseMedicalCondition,
      MedicalCondition,
      QuestionMedicalCondition,
      Question,
      Transaction,
      Plan,
      MedicalCondition,
      PracticeExercise,
    ]),
  ],
}).run([SuperAdminSeeder]);
