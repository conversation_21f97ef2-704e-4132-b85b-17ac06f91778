import { forwardRef, Module } from '@nestjs/common';
import { TransationsService } from './transations.service';
import { TransationsController } from './transations.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Transaction } from './entities/transation.entity';
import { UsersModule } from 'src/users/users.module';
import { AssessmentsModule } from 'src/assessments/assessments.module';
import { PlansModule } from 'src/plans/plans.module';
import { StripeModule } from 'src/stripe/stripe.module';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Transaction]),
    UsersModule,
    AssessmentsModule,
    CommonModule,
    PlansModule,
    forwardRef(() => StripeModule.forRootAsync()),
  ],
  controllers: [TransationsController],
  providers: [TransationsService],
  exports: [TransationsService],
})
export class TransationsModule {}
