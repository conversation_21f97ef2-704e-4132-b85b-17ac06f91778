import { Body, Controller, Post, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { StripeService } from './stripe.service';
import { StripeSubscriptionService } from './services/stripe-subscription.service';

@Controller('webhook')
export class WebhookController {
  constructor(
    private readonly stripeService: StripeService,
    private stripeSubscriptionService: StripeSubscriptionService,
  ) {}

  @Post('stripe')
  async handleStripeWebhook(
    @Req() req: any,
    @Res() res: Response,
    @Body() payload: any,
  ) {
    const sig = req.headers['stripe-signature'] as string;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    try {
      const event = payload;
      // console.log(event);
      // const event = this.stripeService.constructEvent(
      //   payload,
      //   sig,
      //   webhookSecret,
      // );

      switch (event.type) {
        // Payment Intent Succeeded
        case 'payment_intent.succeeded': {
          const paymentIntent = event.data.object;
          await this.stripeService.handlePaymentSuccess(paymentIntent);
          break;
        }

        // Checkout Session Completed
        case 'checkout.session.completed': {
          const session = event.data.object;
          await this.stripeService.handleCheckoutSessionCompleted(session);
          break;
        }

        // Payment Intent Failed
        case 'payment_intent.payment_failed': {
          const paymentIntent = event.data.object;
          await this.stripeService.handlePaymentFailure(paymentIntent);
          break;
        }

        case 'checkout.session.expired': {
          const session = event.data.object;
          await this.stripeSubscriptionService.handleCheckoutSessionExpired(
            session,
          );
          break;
        }
        case 'customer.subscription.updated': {
          const session = event.data.object;
          await this.stripeSubscriptionService.handleSubscriptionUpdated(
            session,
          );
          break;
        }
        case 'invoice.paid': {
          const session = event.data.object;
          await this.stripeSubscriptionService.handleInvoicePaid(session);
        }
        case 'invoice.payment_failed': {
          const session = event.data.object;
          await this.stripeSubscriptionService.handleInvoicePaymentFailed(
            session,
          );
        }

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      // Respond to Stripe to confirm receipt of the event
      res.status(200).send('Webhook received');
    } catch (error) {
      console.error('Webhook Error:', error.message);
      res.status(400).send(`Webhook Error: ${error.message}`);
    }
  }
}
