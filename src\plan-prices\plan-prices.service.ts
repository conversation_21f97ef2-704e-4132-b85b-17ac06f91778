import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { PlanPrice } from './entities/plan-price.entity';

@Injectable()
export class PlanPricesService {
  constructor(
    @InjectRepository(PlanPrice)
    private readonly planPriceRepository: Repository<PlanPrice>,
  ) {}

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[PlanPrice[], number]> {
    return await this.planPriceRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
  }

  async findOneByParam(params: { [key: string]: any }, relations?: string[]) {
    // Find and return the record based on dynamic parameters
    return this.planPriceRepository.findOne({
      where: params,
      relations,
    });
  }
}
