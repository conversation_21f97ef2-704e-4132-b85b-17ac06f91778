import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateHearAndSelectTable1742209013940
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'hear_and_select',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'exercise_type_id',
            type: 'varchar',
            scale: 36,
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['choice_based', 'text_based'],
            isNullable: false,
          },
          {
            name: 'audio_file',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'options',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'correct_answer',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'time_limit',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['exercise_type_id'],
            referencedTableName: 'exercise_types',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('hear_and_select');
  }
}
