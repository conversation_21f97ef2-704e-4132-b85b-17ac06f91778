import { Module } from '@nestjs/common';
import { DownloadableQuestionsService } from './downloadable-questions.service';
import { DownloadableQuestionsController } from './downloadable-questions.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DownloadableQuestion } from './entities/downloadable-question.entity';
import { DownloadableQuestionsAge } from './entities/downloadable-questions-age.entity';
import { DownloadableQuestionsLevel } from './entities/downloadable-questions-level.entity';
import { DownloadableQuestionsMedicalCondition } from './entities/downloadable-questions-medical-conditions.entity';
import { UsersModule } from 'src/users/users.module';
import { CommonModule } from 'src/common/common.module';
import { MedicalConditionModule } from 'src/medical-condition/medical-condition.module';
import { ExerciseTypesModule } from 'src/exercise-types/exercise-types.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DownloadableQuestion,
      DownloadableQuestionsAge,
      DownloadableQuestionsLevel,
      DownloadableQuestionsMedicalCondition,
    ]),
    UsersModule,
    CommonModule,
    MedicalConditionModule,
    ExerciseTypesModule,
  ],
  controllers: [DownloadableQuestionsController],
  providers: [DownloadableQuestionsService],
})
export class DownloadableQuestionsModule {}
