import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateChildDto } from './dto/create-child.dto';
import { UpdateChildDto } from './dto/update-child.dto';
import { CreateChildInterface } from './interface/create-child.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { ChildEntity, Repository } from 'typeorm';
import { Child } from './entities/child.entity';

@Injectable()
export class ChildrenService {
  constructor(
    @InjectRepository(Child)
    private readonly childRepository: Repository<Child>,
  ) {}
  async create(createChildInterface: CreateChildInterface) {
    const newChild = await this.childRepository.create(createChildInterface);
    return await this.childRepository.save(newChild);
  }

  findAll() {}

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ) {
    const [MedicalConditions, total] = await this.childRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
    return [MedicalConditions, total];
  }

  async findOne(id: string) {
    return await this.childRepository.findOne({ where: { id } });
  }

  async update(id: string, updateChildDto: UpdateChildDto): Promise<Child> {
    const child = await this.childRepository.findOne({ where: { id } });

    if (!child) {
      throw new NotFoundException(`Child with ID #${id} not found`);
    }
    const updateFields = Object.keys(updateChildDto).reduce((fields, key) => {
      if (updateChildDto[key] !== undefined) {
        fields[key] = updateChildDto[key];
      }
      return fields;
    }, {} as Partial<UpdateChildDto>);
    if (Object.keys(updateFields).length === 0) {
      throw new BadRequestException('No fields provided to update');
    }
    Object.assign(child, updateFields);
    return await this.childRepository.save(child);
  }

  async remove(id: string) {
    const child = await this.childRepository.findOne({ where: { id } });
    if (!child) {
      throw new NotFoundException(`Child with ID #${id} not found`);
    }
    return await this.childRepository.delete(id);
  }

  async findOneByParam(params: { [key: string]: any }, relations?: string[]) {
    // Find and return the record based on dynamic parameters
    return this.childRepository.findOne({
      where: params,
      relations,
    });
  }

  async findByParam(params: { [key: string]: any }, relations?: string[]) {
    // Find and return the record based on dynamic parameters
    return this.childRepository.find({
      where: params,
      relations,
    });
  }

  async updateCustomerId(child_id: string, customer_id: string) {
    return await this.childRepository.update(
      { id: child_id },
      { stripe_customer_id: customer_id },
    );
  }

  async saveInstance(child: Child) {
    return await this.childRepository.save(child);
  }
}
