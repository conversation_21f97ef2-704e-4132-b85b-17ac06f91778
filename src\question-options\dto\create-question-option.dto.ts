import { <PERSON><PERSON><PERSON>, <PERSON>NotE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import {
  OptionTypeEnum,
  QuestionOptionTypeEnum,
} from '../entities/question-option.entity';

export class CreateQuestionOptionDto {
  @IsString()
  @IsNotEmpty()
  option: string;

  @IsEnum(OptionTypeEnum)
  @IsNotEmpty()
  type: OptionTypeEnum;

  @IsEnum(QuestionOptionTypeEnum)
  @IsNotEmpty()
  question_type: QuestionOptionTypeEnum;
}
