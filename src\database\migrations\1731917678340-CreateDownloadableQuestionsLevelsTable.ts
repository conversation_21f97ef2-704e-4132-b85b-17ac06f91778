import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateDownloadableQuestionsLevelsTable1731917678340
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'downloadable_questions_levels',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'downloadable_questions_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'downloadable_question_types_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'level',
            type: 'int',
            isNullable: false,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['downloadable_questions_id'],
            referencedTableName: 'downloadable_questions',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['downloadable_question_types_id'],
            referencedTableName: 'downloadable_question_types',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('downloadable_questions_levels');
  }
}
