import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateExersiseTypeTable1732516656800
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `exercise_types` MODIFY COLUMN `exercise` ENUM("practice_exercise","option_question","downloadable_question") DEFAULT "practice_exercise";',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `exercise_types` MODIFY COLUMN `exercise` ENUM("practice_exercise","option_question") DEFAULT "practice_exercise";',
    );
  }
}
