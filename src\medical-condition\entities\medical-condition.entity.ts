import { AssessmentMedicalCondition } from 'src/assessments/entities/assessment-medical-condition.entity';
import { Assessment } from 'src/assessments/entities/assessment.entity';
import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';
import { ExerciseMedicalCondition } from 'src/exercise/entities/exersice-medical-condition.entity';
import { PracticeExercise } from 'src/practice-exercises/entities/practice-exercise.entity';
import { QuestionMedicalCondition } from 'src/questions/entities/question-medical-condition.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
} from 'typeorm';

@Entity('medical_condition')
export class MedicalCondition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;

  @OneToMany(
    () => QuestionMedicalCondition,
    (questionMedicalCondition) => questionMedicalCondition.medical_condition,
  )
  questionMedicalConditions: QuestionMedicalCondition[];

  @ManyToMany(() => Assessment, (assessment) => assessment.medicalConditions)
  assessments: Assessment[];

  @ManyToMany(() => PracticeExercise, (exercise) => exercise.medicalConditions)
  practiceExercises: PracticeExercise[];

  @ManyToMany(
    () => ExerciseType,
    (exerciseType) => exerciseType.medicalConditions,
  )
  exerciseType: ExerciseType[];

  @OneToMany(
    () => ExerciseMedicalCondition,
    (exerciseMedicalCondition) => exerciseMedicalCondition.medicalCondition,
  )
  exerciseMedicalConditions: ExerciseMedicalCondition[];

  @OneToMany(
    () => AssessmentMedicalCondition,
    (assessmentMedicalCondition) => assessmentMedicalCondition.medicalCondition,
  )
  assessmentMedicalConditions: AssessmentMedicalCondition[];
}
