import { Child } from 'src/children/entities/child.entity';
import { UserOtp } from 'src/user-otp/entities/user-otp.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  BeforeInsert,
  OneToOne,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { Transaction } from 'src/transations/entities/transation.entity';
import { Subscription } from '../../subscriptions/entities/subscription.entity';

export enum UserType {
  ADMIN = 'super_admin',
  TEACHER = 'teacher',
  PARENT = 'parent',
}

export function encodedPassword(rawPassword: string) {
  const SALT = bcrypt.genSaltSync();
  return bcrypt.hashSync(rawPassword, SALT);
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ type: 'varchar' })
  password: string;
  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      this.password = await encodedPassword(this.password);
    }
  }

  @Column({
    type: 'enum',
    enum: UserType,
  })
  user_type: UserType;

  @Column({ type: 'varchar', nullable: true })
  profile_image: string;

  @Column({ type: 'varchar', nullable: true })
  phone_number: string;

  @Column({ type: 'varchar', nullable: true })
  stripe_customer_id: string;

  @Column({ type: 'boolean', default: false })
  is_email_verified: boolean;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deleted_at?: Date;

  @OneToOne(() => UserOtp, (userOtp) => userOtp.user, { cascade: true })
  otp: UserOtp;

  @OneToMany(() => Child, (child) => child.user)
  children: Child[];

  @OneToMany(() => Transaction, (transaction) => transaction.plan)
  transactions: Transaction[];

  @OneToMany(() => Subscription, (subscription) => subscription.user, {
    cascade: true,
  })
  subscriptions: Subscription[];
}
