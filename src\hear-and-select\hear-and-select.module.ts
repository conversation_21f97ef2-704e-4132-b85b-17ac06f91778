import { Modu<PERSON> } from '@nestjs/common';
import { HearAndSelectService } from './hear-and-select.service';
import { HearAndSelectController } from './hear-and-select.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HearAndSelect } from './entities/hear-and-select.entity';
import { CommonModule } from 'src/common/common.module';
import { UsersModule } from 'src/users/users.module';
import { ExerciseTypesModule } from 'src/exercise-types/exercise-types.module';
import { HearAndSelectAge } from './entities/hear-and-select-age.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([HearAndSelect, HearAndSelectAge]),
    CommonModule,
    UsersModule,
    ExerciseTypesModule,
  ],
  controllers: [HearAndSelectController],
  providers: [HearAndSelectService],
})
export class HearAndSelectModule {}
