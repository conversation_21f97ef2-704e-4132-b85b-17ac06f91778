import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Assessment } from './assessment.entity';
import { Question } from 'src/questions/entities/question.entity';

@Entity('assessment_questions')
export class AssessmentQuestions {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  assessment_id: string;

  @ManyToOne(() => Assessment, (assessment) => assessment.assessmentQuestions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'assessment_id' })
  assessment: Assessment;

  @Column({ type: 'varchar' })
  question_id: string;

  @ManyToOne(() => Question, (question) => question.assessmentQuestions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'question_id' })
  question: Question;

  @Column({ type: 'varchar' })
  answer: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;
}
