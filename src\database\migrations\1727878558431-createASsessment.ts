import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateASsessment1727878558431 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'assessments',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'child_id',
            type: 'varchar',
          },
          {
            name: 'basic_questions_pass',
            type: 'boolean',
            default: false,
          },
          {
            name: 'basic_question_answered_count',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['IN_PROGRESS', 'COMPLETED'],
            default: "'IN_PROGRESS'",
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
    );
    await queryRunner.createForeignKey(
      'assessments',
      new TableForeignKey({
        columnNames: ['child_id'],
        referencedTableName: 'children',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const assessmentTable = await queryRunner.getTable('assessments');
    const foreignKey = assessmentTable.foreignKeys.find((fk) =>
      fk.columnNames.indexOf('child_id'),
    );
    await queryRunner.dropForeignKey('assessments', foreignKey);
    await queryRunner.dropTable('assessments');
  }
}
