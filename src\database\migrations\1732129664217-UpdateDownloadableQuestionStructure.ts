import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class UpdateDownloadableQuestionStructure1732129664217
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'downloadable_questions',
      new TableColumn({
        name: 'level_id',
        type: 'varchar',
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      'downloadable_questions',
      new TableForeignKey({
        columnNames: ['level_id'],
        referencedTableName: 'downloadable_questions_levels',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );

    const table = await queryRunner.getTable('downloadable_questions_levels');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('downloadable_questions_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey(
        'downloadable_questions_levels',
        foreignKey,
      );
    }

    await queryRunner.dropColumn(
      'downloadable_questions_levels',
      'downloadable_questions_id',
    );

    await queryRunner.addColumn(
      'downloadable_questions',
      new TableColumn({
        name: 'text',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('downloadable_questions', 'text');
    const table = await queryRunner.getTable('downloadable_questions');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('level_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('downloadable_questions', foreignKey);
    }

    await queryRunner.dropColumn('downloadable_questions', 'level_id');
    await queryRunner.addColumn(
      'downloadable_questions_levels',
      new TableColumn({
        name: 'downloadable_questions_id',
        type: 'varchar',
        isNullable: false,
      }),
    );

    // await queryRunner.createForeignKey(
    //   'practice_exercises_levels',
    //   new TableForeignKey({
    //     columnNames: ['practice_exercise_id'],
    //     referencedTableName: 'practice_exercises',
    //     referencedColumnNames: ['id'],
    //     onDelete: 'SET NULL',
    //   }),
    // );
  }
}
