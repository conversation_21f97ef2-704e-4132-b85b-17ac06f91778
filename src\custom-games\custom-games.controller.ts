import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { CustomGamesService } from './custom-games.service';
import { CreateCustomGameDto } from './dto/create-custom-game.dto';
import { UpdateCustomGameDto } from './dto/update-custom-game.dto';
import { ResponseService } from 'src/common/services/response.service';
import { ExerciseTypesService } from 'src/exercise-types/exercise-types.service';
import { ExerciseEnum } from 'src/exercise-types/entities/exercise-type.entity';
import { FindManyOptions, In } from 'typeorm';

@Controller('custom-games')
export class CustomGamesController {
  constructor(
    private readonly customGamesService: CustomGamesService,
    private readonly responseService: ResponseService,
    private readonly exerciseTypeService: ExerciseTypesService,
  ) {}

  @Post()
  async create(@Body() createCustomGameDto: CreateCustomGameDto) {
    const exerciseType = await this.exerciseTypeService.findOne(
      createCustomGameDto.exercise_type_id,
    );
    if (exerciseType.exercise !== ExerciseEnum.CUSTOM_GAME) {
      throw new BadRequestException('Invalid Exercise Type');
    }
    const savedCustomGame =
      await this.customGamesService.create(createCustomGameDto);
    return this.responseService.successResponse(
      'New CustomGame Created Successfully',
      savedCustomGame,
    );
  }

  @Get()
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [customGames, total] =
      await this.customGamesService.findAndCountByParam(where, skip, limit, [
        'exerciseType',
      ]);

    return this.responseService.successResponse('CustomGame List', {
      data: customGames,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get('users')
  async findForUser(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [customGames, total] = await this.customGamesService.findForUser(
      where,
      skip,
      limit,
      ['exerciseType'],
    );

    return this.responseService.successResponse('CustomGame List', {
      data: customGames,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.customGamesService.findOne(id);
    return this.responseService.successResponse('CustomGame Details', data);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateCustomGameDto: UpdateCustomGameDto,
  ) {
    const updatedCustomGame = await this.customGamesService.update(
      id,
      updateCustomGameDto,
    );
    return this.responseService.successResponse(
      'CustomGame Updated Successfully',
      updatedCustomGame,
    );
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.customGamesService.remove(id);
    return this.responseService.successResponse(
      'CustomGame Deleted Successfully',
    );
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};

    if (filter.ages) {
      const agesArray = Array.isArray(filter.ages)
        ? filter.ages
        : filter.ages.split(',').map((age) => parseInt(age.trim(), 10));
      where['ages'] = { age: In(agesArray) };
    }

    if (filter.exercise_type_id) {
      where['exercise_type_id'] = filter.exercise_type_id;
    }

    if (filter.type) {
      where['type'] = filter.type;
    }

    return where;
  }
}
