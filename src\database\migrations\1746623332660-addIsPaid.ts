import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddIsPaid1746623332660 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'exercise_types',
      new TableColumn({
        name: 'is_paid',
        type: 'boolean',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('exercise_types', 'is_paid');
  }
}
