import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  JoinColumn,
  OneToOne,
} from 'typeorm';
import { DownloadableQuestion } from './downloadable-question.entity';
import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';

@Entity('downloadable_questions_levels')
export class DownloadableQuestionsLevel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int' })
  level: number;

  // @Column({ name: 'downloadable_question_types_id', type: 'varchar' })
  // downloadable_question_types_id: string;

  @Column({ type: 'varchar', nullable: true })
  exercise_type_id: string;

  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.downloadableQuestionsLevels,
    {
      onDelete: 'SET NULL',
    },
  )
  @JoinColumn({ name: 'exercise_type_id' })
  exerciseType: ExerciseType;

  // @ManyToOne(() => DownloadableQuestionType, (type) => type.levels, {
  //   nullable: false,
  //   onDelete: 'CASCADE',
  // })
  // @JoinColumn({ name: 'downloadable_question_types_id' })
  // questionType: DownloadableQuestionType;

  @OneToOne(
    () => DownloadableQuestion,
    (downloadableQuestion) => downloadableQuestion.level,
  )
  downloadableQuestion: DownloadableQuestion;
}
