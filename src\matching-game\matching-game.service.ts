import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MatchingGame } from './entities/matching-game.entity';
import { MatchingGameAge } from './entities/matching-game-age.entity';
import { MatchingGameItems } from './entities/matching-game-items.entity';
import { CreateMatchingGameDto } from './dto/create-matching-game.dto';
import { UpdateMatchingGameDto } from './dto/update-matching-game.dto';

@Injectable()
export class MatchingGameService {
  constructor(
    @InjectRepository(MatchingGame)
    private readonly matchingGameRepository: Repository<MatchingGame>,
    @InjectRepository(MatchingGameAge)
    private readonly matchingGameAgeRepository: Repository<MatchingGameAge>,
    @InjectRepository(MatchingGameItems)
    private readonly matchingGameItemsRepository: Repository<MatchingGameItems>,
  ) {}

  async create(createMatchingGameDto: CreateMatchingGameDto) {
    const { ages, items, ...data } = createMatchingGameDto;
    const newMatchingGame = this.matchingGameRepository.create(data);
    const savedMatchingGame =
      await this.matchingGameRepository.save(newMatchingGame);

    if (ages && ages.length > 0) {
      await this.addAgesToMatchingGame(savedMatchingGame, ages);
    }

    if (items && items.length > 0) {
      await this.addItemsToMatchingGame(savedMatchingGame, items);
    }

    return savedMatchingGame;
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[MatchingGame[], number]> {
    return await this.matchingGameRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
  }

  async findOne(id: string) {
    const matchingGame = await this.matchingGameRepository.findOne({
      where: { id },
      relations: ['exerciseType', 'ages', 'items'],
    });
    if (!matchingGame) {
      throw new NotFoundException(`MatchingGame with ID ${id} not found`);
    }
    return matchingGame;
  }

  async update(id: string, updateMatchingGameDto: UpdateMatchingGameDto) {
    const matchingGame = await this.findOne(id);
    const { ages, items, ...data } = updateMatchingGameDto;

    await this.matchingGameRepository.update(id, data);

    if (ages && ages.length > 0) {
      await this.matchingGameAgeRepository.delete({ matchingGame });
      await this.addAgesToMatchingGame(matchingGame, ages);
    }

    if (items && items.length > 0) {
      await this.matchingGameItemsRepository.delete({ matchingGame });
      await this.addItemsToMatchingGame(matchingGame, items);
    }

    return await this.findOne(id);
  }

  async remove(id: string) {
    return await this.matchingGameRepository.delete(id);
  }

  async addAgesToMatchingGame(matchingGame: MatchingGame, ages: number[]) {
    const ageEntities = ages.map((age) => {
      const ageEntity = new MatchingGameAge();
      ageEntity.age = age;
      ageEntity.matchingGame = matchingGame;
      return ageEntity;
    });
    return await this.matchingGameAgeRepository.save(ageEntities);
  }

  async addItemsToMatchingGame(
    matchingGame: MatchingGame,
    items: { item_left: string; item_right: string }[],
  ) {
    const itemEntities = items.map((item) => {
      const itemEntity = new MatchingGameItems();
      itemEntity.item_left = item.item_left;
      itemEntity.item_right = item.item_right;
      itemEntity.matchingGame = matchingGame;
      return itemEntity;
    });
    return await this.matchingGameItemsRepository.save(itemEntities);
  }
}
