import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  OneToMany,
} from 'typeorm';
import { User } from 'src/users/entities/user.entity';
import { Class } from 'src/classes/entities/class.entity';
import { Assessment } from 'src/assessments/entities/assessment.entity';
import { ExerciseResult } from 'src/exercise-results/entities/exercise-result.entity';
import { Subscription } from '../../subscriptions/entities/subscription.entity';
import { Transaction } from 'src/transations/entities/transation.entity';
import { BillingAddressChild } from '../interface/create-child.interface';

export enum Gender {
  MALE = 'Male',
  FEMALE = 'Female',
  OTHER = 'Other',
}

export enum HandPreference {
  RIGHT = 'Right',
  LEFT = 'Left',
  BOTH = 'Both',
}

export enum AcadamicPerformance {
  EXCELLENT = 'Excellent',
  GOOD = 'Good',
  POOR = 'Average',
  BELOW_AVARAGE = 'Below_Average',
}
@Entity('children')
export class Child {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, (user) => user.children, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'int' })
  age: number;

  @Column({ type: 'boolean', default: true })
  is_active: boolean;

  @Column({ type: 'varchar' })
  user_id: string;

  @Column({ type: 'varchar' })
  class_id: string;

  @Column({ type: 'varchar' })
  stripe_customer_id: string;

  @Column({ type: 'date' })
  date_of_birth: Date;

  @Column({
    type: 'enum',
    enum: Gender,
  })
  gender: Gender;

  @Column({ type: 'varchar', nullable: true })
  country: string;

  @Column({ type: 'varchar', nullable: true })
  language: string;

  @Column({
    type: 'enum',
    enum: HandPreference,
    nullable: true,
  })
  hand_preference: string;

  @Column({
    type: 'enum',
    enum: ['Below Average', 'Average', 'Good', 'Excellent'],
    nullable: true,
  })
  acadamic_performance: string;

  @Column('simple-json', { nullable: true })
  billing_address?: BillingAddressChild;

  @ManyToOne(() => Class, (cls) => cls.children, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'class_id' })
  class: Class;

  @Column({ type: 'varchar', nullable: true })
  profile_image: string;

  @CreateDateColumn({ type: 'timestamp' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true })
  deleted_at?: Date;

  @OneToMany(() => Assessment, (assessment) => assessment.child, {
    cascade: true,
  })
  assessments: Assessment[];

  @OneToMany(() => ExerciseResult, (exerciseResult) => exerciseResult.child, {
    cascade: true,
  })
  exerciseResults: ExerciseResult[];

  @OneToMany(() => Subscription, (subscription) => subscription.child, {
    cascade: true,
  })
  subscriptions: Subscription[];

  @OneToMany(() => Transaction, (transaction) => transaction.child)
  transactions: Transaction[];
}
