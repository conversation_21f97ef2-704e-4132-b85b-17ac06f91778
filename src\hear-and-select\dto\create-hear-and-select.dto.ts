// src/modules/hear-and-select/dto/create-hear-and-select.dto.ts

import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsInt,
  IsJSON,
  ValidateIf,
  IsArray,
  IsNumber,
} from 'class-validator';
import { HearAndSelectType } from '../entities/hear-and-select.entity';

export class CreateHearAndSelectDto {
  @IsString()
  @IsNotEmpty()
  exercise_type_id: string;

  @IsEnum(HearAndSelectType)
  @IsNotEmpty()
  type: HearAndSelectType;

  @IsString()
  @IsNotEmpty()
  audio_file: string;

  @ValidateIf((obj) => obj.type === HearAndSelectType.CHOICE_BASED)
  @IsArray()
  @IsNotEmpty()
  options: Record<string, any>;

  @IsString()
  @IsOptional()
  correct_answer?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  text?: string;

  @IsInt()
  @IsOptional()
  time_limit?: number;

  @IsNumber()
  @IsOptional()
  level: number;

  @IsArray()
  @IsNotEmpty()
  ages: number[];
}
