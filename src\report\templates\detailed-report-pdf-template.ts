import * as PDFDocument from 'pdfkit';
import { getScoreLabel } from './basic-report-pdf-template';

export function createDetailedReportPDF(reportData): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const doc = new PDFDocument({
      size: 'LETTER',
      bufferPages: true,
    });
    const buffers: Buffer[] = [];
    const overallScore =
      (Number(reportData.tracingScore) + Number(reportData.imageTracingScore)) /
      2;

    doc.on('data', buffers.push.bind(buffers));
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(buffers);
      resolve(pdfBuffer);
    });

    // Title and Intro
    doc
      .fontSize(20)
      .font('Helvetica-Bold')
      .text('Scribblesense Assessment Report', { align: 'center' })
      .font('Helvetica')
      .moveDown(2);

    doc
      .fontSize(12)
      .text(`Test Date: ${reportData.testDate}`, { align: 'right' })
      .moveDown();

    doc
      .text(`Name: ${reportData.name}`)
      .text(`Date of Birth: ${reportData.dob}`)
      .text(`Age: ${reportData.age}`)
      .text(`Gender: ${reportData.gender}`)
      .text(`School Year / Educational Level: ${reportData.schoolYear}`)
      // .text(`Language of Education: ${reportData.language}`)
      .text(`Country: ${reportData.country}`)
      .moveDown();

    let pronouns;
    if (reportData.gender == 'male') {
      pronouns = 'his';
    } else if (reportData.gender == 'female') {
      pronouns = 'her';
    }

    doc
      .text(
        `${reportData.name} has participated in a focused assessment to identify potential signs of writing difficulty that affects writing skills. This report summarizes ${pronouns} performance on writing-related tasks, along with feedback from ${pronouns} parent, highlighting any concerns about ${pronouns} writing ability.`,
      )
      .moveDown(2);

    // Results Summary
    doc.fontSize(14).text('Results Summary', { underline: true }).moveDown();

    doc
      .fontSize(12)
      .text(`Overall Performance: ${getScoreLabel(overallScore)}`)
      .moveDown()
      .text(
        `Word Tracing Exercise Score: ${getScoreLabel(reportData.tracingScore)}`,
      )
      .moveDown()
      .text(
        `Image Tracing Score: ${getScoreLabel(reportData.imageTracingScore)}`,
      )
      .moveDown();

    // Areas of Concern
    doc.fontSize(14).text('Areas of Concern:', { underline: true }).moveDown();
    reportData.areasOfConcern.forEach((concern) => {
      doc.text(`- ${concern}`);
    });

    doc.addPage();

    // Detailed Results - Word Tracing Exercise
    doc
      .fontSize(15)
      .font('Helvetica-Bold')
      .text(
        'The following section details his/her performance in each of the writing-related activities:',
      )
      .font('Helvetica')
      .moveDown();

    doc
      .fontSize(14)
      .text('a) Word Tracing Exercise', { underline: true })
      .moveDown()
      .fontSize(12)
      .text(
        `${reportData.name} was asked to trace specific words on-screen, forming words freehand.`,
      )
      .moveDown();

    doc.text('Result', { underline: true }).moveDown();
    doc.text(`Score: ${reportData.tracingScore} %`, { indent: 20 }).moveDown();
    doc.text(`Observations:`, { underline: true }).moveDown();

    // Performance Commentary
    const tracingScore = Number(reportData.tracingScore);
    if (tracingScore <= 60) {
      doc.text(`${reportData.name} did poorly in the word tracing test`);
    } else if (tracingScore <= 70) {
      doc.text(`${reportData.name} performed below average in word tracing`);
    } else if (tracingScore <= 80) {
      doc.text(`${reportData.name} performed average in word tracing`);
    } else if (tracingScore <= 100) {
      doc.text(`${reportData.name} performed above average in word tracing`);
    }

    doc.moveDown();

    // Medical Conditions Based on Tracing Score
    if (tracingScore < 60) {
      doc.text(`This suggests significant difficulty with:`).moveDown();
      doc
        .font('Helvetica-Bold')
        .text('1. Fine motor difficulties:', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' This could manifest in slow writing speed, poor letter formation, and inconsistent spacing between words.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('2. Spatial difficulties:', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' This could be evident in the inconsistent alignment of letters and poor word spacing.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('3. Spelling difficulties:', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' This is indicated in challenges with phonological awarness, ability to recognize and manipulate sounds in words.',
        )
        .moveDown();
      doc.text(`Which are key indicators of dysgraphia.`).moveDown();
    } else {
      doc.text('No significant issues found').moveDown();
    }

    // Detailed Results - Image Tracing Exercise
    doc.addPage();
    doc
      .fontSize(14)
      .text('b) Image Tracing Exercise', { underline: true })
      .moveDown()
      .fontSize(12)
      .text(
        `${reportData.name} was asked to trace a given shape displayed on-screen.`,
      )
      .moveDown();

    doc.text('Result', { underline: true }).moveDown();
    doc
      .text(`Score: ${reportData.imageTracingScore} %`, {
        indent: 20,
      })
      .moveDown();

    doc.text(`Observations:`, { underline: true }).moveDown();
    const imageTracingScore = Number(reportData.imageTracingScore);
    if (imageTracingScore <= 60) {
      doc.text(`${reportData.name} performed poorly in the image tracing test`);
    } else if (imageTracingScore <= 70) {
      doc.text(`${reportData.name} performed below average in image tracing`);
    } else if (imageTracingScore <= 80) {
      doc.text(`- ${reportData.name} performed average in image tracing`);
    } else if (imageTracingScore <= 100) {
      doc.text(`${reportData.name} performed above average in image tracing`);
    }

    doc.moveDown();

    // Medical Conditions Based on Image Tracing Score
    if (imageTracingScore < 60) {
      doc.text(`This suggests difficulty with:`);
      doc
        .font('Helvetica-Bold')
        .text('1. Fine Motor Difficulties :-', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' Issues in holding pencil, line quality, The child may press too hard or too lightly on the pencil, leading to indentations or faint lines.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('2. Hand-Eye Coordination :-', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' The tracing may be inaccurate, with the line deviating from the intended path.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('3. Spatial Difficulties :-', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' The child may have difficulty orienting the image correctly on the paper, leading to reversed or upside-down tracing.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('4. Perceptual Issues :-', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' The tracing may be incomplete or inaccurate, with the child missing parts of the image or tracing the wrong lines.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('5. Motor Planning :-', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' The tracing may be hesitant or jerky, with the child pausing frequently to plan the next movement.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('6. Visual-Motor Integration :-', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ' The tracing may be inaccurate, with the child struggling to coordinate their hand movements with their visual perception.',
        )
        .moveDown();
      doc
        .font('Helvetica-Bold')
        .text('7. Attention and Focus :-', {
          indent: 10,
          continued: true,
        })
        .font('Helvetica')
        .text(
          ` The tracing may be inconsistent, with the child's attention wandering and leading to errors or omissions.`,
        )
        .moveDown();
    } else {
      doc.text('No significant issues found').moveDown();
    }

    // Conclusion
    doc
      .fontSize(14)
      .text('Conclusion', { underline: true })
      .moveDown()
      .fontSize(12)
      .text(
        `Based on the writing assessment and questionnaire based assessment we suggest that ${reportData.name} have issues in following areas`,
      );

    reportData.areasOfConcern.forEach((condition, index) => {
      doc.text(`${index + 1}. ${condition}`, {
        indent: 10,
      });
    });

    // Finalize PDF
    doc.end();

    doc.on('error', (error) => {
      reject(error);
    });
  });
}
