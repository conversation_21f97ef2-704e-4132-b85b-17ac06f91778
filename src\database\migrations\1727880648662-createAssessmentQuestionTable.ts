import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateAssessmentQuestionTable1727880648662
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'assessment_questions',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'assessment_id',
            type: 'varchar',
          },
          {
            name: 'question_id',
            type: 'varchar',
          },
          {
            name: 'answer',
            type: 'varchar',
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
    );

    await queryRunner.createForeignKey(
      'assessment_questions',
      new TableForeignKey({
        columnNames: ['assessment_id'],
        referencedTableName: 'assessments',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'assessment_questions',
      new TableForeignKey({
        columnNames: ['question_id'],
        referencedTableName: 'questions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('assessment_questions');

    const assessmentForeignKey = table.foreignKeys.find((fk) =>
      fk.columnNames.includes('assessment_id'),
    );
    const questionForeignKey = table.foreignKeys.find((fk) =>
      fk.columnNames.includes('question_id'),
    );
    if (assessmentForeignKey) {
      await queryRunner.dropForeignKey(
        'assessment_questions',
        assessmentForeignKey,
      );
    }
    if (questionForeignKey) {
      await queryRunner.dropForeignKey(
        'assessment_questions',
        questionForeignKey,
      );
    }

    await queryRunner.dropTable('assessment_questions');
  }
}
