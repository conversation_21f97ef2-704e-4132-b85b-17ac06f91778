import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CustomGamesService } from './custom-games.service';
import { CustomGamesController } from './custom-games.controller';
import { CustomGame } from './entities/custom-game.entity';
import { CustomGameAge } from './entities/custom-game-age.entity';
import { CommonModule } from 'src/common/common.module';
import { ExerciseTypesModule } from 'src/exercise-types/exercise-types.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CustomGame, CustomGameAge]),
    CommonModule,
    ExerciseTypesModule, // Register entities
  ],
  controllers: [CustomGamesController],
  providers: [CustomGamesService],
})
export class CustomGamesModule {}
