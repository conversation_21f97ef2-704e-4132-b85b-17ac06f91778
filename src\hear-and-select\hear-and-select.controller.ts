import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  BadRequestException,
  Query,
} from '@nestjs/common';
import { HearAndSelectService } from './hear-and-select.service';
import { CreateHearAndSelectDto } from './dto/create-hear-and-select.dto';
import { UpdateHearAndSelectDto } from './dto/update-hear-and-select.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Role } from 'src/common/decorators/roles.decorator';
import { ExerciseTypesService } from 'src/exercise-types/exercise-types.service';
import { ResponseService } from 'src/common/services/response.service';
import { FindManyOptions, In, Like } from 'typeorm';

@Controller('hear-and-select')
export class HearAndSelectController {
  constructor(
    private readonly hearAndSelectService: HearAndSelectService,
    private readonly exerciseTypeService: ExerciseTypesService,
    private readonly responseService: ResponseService,
  ) {}

  // @UseGuards(AccessTokenGuard, RolesGuard)
  // @Role('super_admin')
  @Post()
  async create(@Body() createHearAndSelectDto: CreateHearAndSelectDto) {
    const question_type = await this.exerciseTypeService.findOne(
      createHearAndSelectDto.exercise_type_id,
    );
    if (question_type.exercise !== 'hear_and_select') {
      throw new BadRequestException('Invalid Exercise Type');
    }
    const savedHearAndSelect = await this.hearAndSelectService.create(
      createHearAndSelectDto,
    );

    await this.hearAndSelectService.addAgeToHearAndSelect(
      savedHearAndSelect,
      createHearAndSelectDto.ages,
    );
    return this.responseService.successResponse(
      'New HearAndSelect Question Created Successfully',
      savedHearAndSelect,
    );
  }
  @Get()
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [downloadableQuestions, total] =
      await this.hearAndSelectService.findAndCountByParam(where, skip, limit, [
        'exerciseType',
      ]);

    return this.responseService.successResponse('HearAndSelect List', {
      data: downloadableQuestions,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get('users')
  async findAllUsers(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [data, total] = await this.hearAndSelectService.findAndCountByParam(
      where,
      skip,
      limit,
      ['exerciseType'],
    );

    return this.responseService.successResponse('HearAndSelect List', {
      data: data,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }
  // @Get()
  // findAll() {
  //   return this.hearAndSelectService.findAll();
  // }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.hearAndSelectService.findOne(id);
    return this.responseService.successResponse(
      'HearAndSelect Question Details',
      data,
    );
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateHearAndSelectDto: UpdateHearAndSelectDto,
  ) {
    const savedHearAndSelect = await this.hearAndSelectService.update(
      id,
      updateHearAndSelectDto,
    );

    if (updateHearAndSelectDto.ages && updateHearAndSelectDto.ages.length) {
      await this.hearAndSelectService.addAgeToHearAndSelect(
        savedHearAndSelect,
        updateHearAndSelectDto.ages,
      );
    }
    return this.responseService.successResponse(
      ' HearAndSelect updated Successfully',
    );
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.hearAndSelectService.remove(id);
    return this.responseService.successResponse(
      'Downloadable Question deleted successfully',
    );
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.ages) {
      const agesArray = Array.isArray(filter.ages)
        ? filter.ages
        : filter.ages.split(',').map((age) => parseInt(age.trim(), 10));
      where['ages'] = { age: In(agesArray) };
    }
    if (filter.question_type_id) {
      where['question_type_id'] = filter.question_type_id;
    }
    if (filter.exercise_type_id) {
      where['exercise_type_id'] = filter.exercise_type_id;
    }
    if (filter.type) {
      where['type'] = filter.type;
    }
    return where;
  }
}
