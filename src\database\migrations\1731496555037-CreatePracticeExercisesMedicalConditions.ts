import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePracticeExercisesMedicalConditions1731496555037
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'practice_exercises_medical_conditions',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'practice_exersise_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'medical_condition_id',
            type: 'varchar',
            isNullable: false,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['practice_exersise_id'],
            referencedTableName: 'practice_exercises',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['medical_condition_id'],
            referencedTableName: 'medical_condition',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('practice_exercises_medical_conditions');
  }
}
