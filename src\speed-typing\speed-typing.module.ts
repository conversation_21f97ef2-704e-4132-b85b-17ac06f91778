import { Modu<PERSON> } from '@nestjs/common';
import { SpeedTypingService } from './speed-typing.service';
import { SpeedTypingController } from './speed-typing.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SpeedTypingAge } from './entities/speed-typing-age.entity';
import { SpeedTyping } from './entities/speed-typing.entity';
import { CommonModule } from 'src/common/common.module';
import { ExceptionsHandler } from '@nestjs/core/exceptions/exceptions-handler';
import { ExerciseTypesModule } from 'src/exercise-types/exercise-types.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SpeedTyping, SpeedTypingAge]),
    CommonModule,
    ExerciseTypesModule,
  ],
  controllers: [SpeedTypingController],
  providers: [SpeedTypingService],
})
export class SpeedTypingModule {}
