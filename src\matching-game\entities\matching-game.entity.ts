import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { ExerciseType } from '../../exercise-types/entities/exercise-type.entity';
import { MatchingGameAge } from './matching-game-age.entity';
import { MatchingGameItems } from './matching-game-items.entity';

export enum MatchingGameType {
  TEXT_TO_TEXT = 'text_to_text',
  IMAGE_TO_TEXT = 'image_to_text',
}

@Entity('matching_game')
export class MatchingGame {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  exercise_type_id: string;

  @Column({ type: 'enum', enum: MatchingGameType })
  type: MatchingGameType;

  @Column()
  question: string;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'int', nullable: true })
  time_limit: number;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;

  // relations
  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.hearAndSelects,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'exercise_type_id' }) // Explicitly specify the column name
  exerciseType: ExerciseType;

  @OneToMany(
    () => MatchingGameAge,
    (matchingGameAge) => matchingGameAge.matchingGame,
    {
      cascade: true,
    },
  )
  ages: MatchingGameAge[];

  @OneToMany(
    () => MatchingGameItems,
    (matchingGameItems) => matchingGameItems.matchingGame,
  )
  items: MatchingGameItems[];
}
