import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateOptionQuestionsLevelsTable1731580368428
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'option_questions_levels',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'option_question_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'exersise_type_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'level',
            type: 'int',
            isNullable: false,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['option_question_id'],
            referencedTableName: 'option_questions',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['exersise_type_id'],
            referencedTableName: 'exercise_types',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('option_questions_levels');
  }
}
