import { Injectable } from '@nestjs/common';
import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';
import { Plan, PlanType } from './entities/plan.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreatePLanInterface } from './interface/create-plan.interface';
import { Subscription, SubscriptionStatus } from 'src/subscriptions/entities/subscription.entity';

@Injectable()
export class PlansService {
  constructor(
    @InjectRepository(Plan)
    private planRepository: Repository<Plan>,
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
  ) {}

  async create(createPlanInterface: CreatePLanInterface) {
    const newPlan = this.planRepository.create(createPlanInterface);
    return await this.planRepository.save(newPlan);
  }

  async findAll() {
    return await this.planRepository.find();
  }

async findAllSubscriptions(filterDto: any) {
  const { child_id } = filterDto;

  const relations = ['planPrices'];

  const plans = await this.planRepository.find({
    where: {
      type: PlanType.SUBSCRIPTION,
      is_published: true,
    },
    relations,
  });

  // If child_id not provided, just return plans with is_purchased = false
  if (!child_id) {
    return plans.map(plan => ({
      ...plan,
      is_purchased: false,
      planPrices: plan.planPrices.map(price => ({
        ...price,
        is_purchased: false,
      })),
    }));
  }

  // Get all active subscriptions for the child
  const activeSubscriptions = await this.subscriptionRepository.find({
    where: {
      child_id,
      status: SubscriptionStatus.ACTIVE,
    },
    select: ['plan_id','id','child_id'],
  });

  const purchasedPlanIds = new Set(activeSubscriptions.map(s => s.plan_id));


  // Mark plans and prices accordingly
  return plans.map(plan => {
    const isPurchased = purchasedPlanIds.has(plan.id);
    return {
      ...plan,
      is_purchased: isPurchased,
      planPrices: plan.planPrices.map(price => ({
        ...price,
        is_purchased: isPurchased,
      })),
    };
  });
}


  async findOne(id: string) {
    return await this.planRepository.findOne({ where: { id } });
  }

  async update(id: string, updatePlanDto: UpdatePlanDto) {
    await this.planRepository.update(id, updatePlanDto);
    return await this.findOne(id);
  }

  async remove(id: string) {
    const planToRemove = await this.findOne(id);
    if (planToRemove) {
      return await this.planRepository.remove(planToRemove);
    }
    throw new Error(`Plan with ID ${id} not found`);
  }
}
