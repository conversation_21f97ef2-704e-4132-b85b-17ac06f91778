import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateDownloadableQuestionDto {
  @IsNotEmpty()
  @IsString()
  question_file: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  exercise_type_id: string;

  @IsOptional()
  @IsString()
  thumbnail?: string;

  @IsNumber()
  @IsOptional()
  level: number;

  @IsArray()
  @IsNotEmpty()
  ages: number[];

  @IsOptional()
  @IsString()
  text?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Type(() => String)
  medical_condition_ids?: string[];
}
