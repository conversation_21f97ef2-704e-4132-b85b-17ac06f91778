import { forwardRef, Module } from '@nestjs/common';
import { UserOtpService } from './user-otp.service';
import { UserOtpController } from './user-otp.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserOtp } from './entities/user-otp.entity';
import { OtpModule } from 'src/otp/otp.module';
import { UsersModule } from 'src/users/users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserOtp]),
    OtpModule,
    forwardRef(() => UsersModule),
  ],
  controllers: [UserOtpController],
  providers: [UserOtpService],
  exports: [UserOtpService],
})
export class UserOtpModule {}
