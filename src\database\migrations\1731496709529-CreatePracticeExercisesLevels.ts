import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreatePracticeExercisesLevels1731496709529
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'practice_exercises_levels',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'practice_exercise_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'practice_exercise_type_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'level',
            type: 'int',
            isNullable: false,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['practice_exercise_id'],
            referencedTableName: 'practice_exercises',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['practice_exercise_type_id'],
            referencedTableName: 'exercise_types',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('practice_exercises_levels');
  }
}
