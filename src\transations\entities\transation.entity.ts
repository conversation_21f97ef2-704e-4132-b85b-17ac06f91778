import { Assessment } from 'src/assessments/entities/assessment.entity';
import { Child } from 'src/children/entities/child.entity';
import { PlanPrice } from 'src/plan-prices/entities/plan-price.entity';
import { Plan } from 'src/plans/entities/plan.entity';
import { User } from 'src/users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

export enum TransactionType {
  SUBSCRIPTION = 'subscription',
  OTHER = 'other',
}

@Entity('transactions')
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Assessment, (assessment) => assessment.transactions)
  @JoinColumn({ name: 'assessment_id' })
  assessment: Assessment;

  @Column({ type: 'varchar' })
  assessment_id: string;

  @ManyToOne(() => User, (user) => user.transactions)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'varchar' })
  user_id: string;

  @Column({ type: 'varchar' })
  subscription_id: string;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column('decimal', { precision: 10, scale: 2 })
  amount: number;

  @Column({ type: 'varchar' })
  payment_indent: string;

  @ManyToOne(() => Plan, (plan) => plan.transactions)
  @JoinColumn({ name: 'plan_id' })
  plan: Plan;

  @Column()
  session_id: string;

  @Column({ nullable: true })
  note: string;

  @Column({ type: 'varchar' })
  plan_id: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;

  @ManyToOne(() => Child, (child) => child.transactions, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'child_id' })
  child: Child;

  @Column({ nullable: true })
  child_id: string;

  @ManyToOne(() => PlanPrice, (planPrice) => planPrice.transactions, {
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'plan_price_id' })
  planPrice: PlanPrice;

  @Column({ nullable: true })
  plan_price_id: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
    nullable: true,
  })
  type: TransactionType;
}
