import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { ExerciseResult } from './entities/exercise-result.entity';
import { CreateExerciseResultDto } from './dto/create-exercise-result.dto';

@Injectable()
export class ExerciseResultsService {
  constructor(
    @InjectRepository(ExerciseResult)
    private readonly exerciseResultRepository: Repository<ExerciseResult>,
  ) {}

  async create(createExerciseResultDto: CreateExerciseResultDto) {
    const newResult = this.exerciseResultRepository.create(
      createExerciseResultDto,
    );
    return await this.exerciseResultRepository.save(newResult);
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[ExerciseResult[], number]> {
    return await this.exerciseResultRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
  }

  async findAnalyticsOfChild(child_id: string) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());

    const startOfMonth = new Date(today);
    startOfMonth.setDate(1);

    const results = await this.exerciseResultRepository
      .createQueryBuilder('exerciseResult')
      .select([
        "SUM(CASE WHEN exerciseResult.result = 'success' THEN 1 ELSE 0 END) AS totalSuccess",
        "SUM(CASE WHEN exerciseResult.result = 'fail' THEN 1 ELSE 0 END) AS totalFail",
        'SUM(exerciseResult.time_taken) AS totalTimeTaken',
      ])
      .addSelect([
        'SUM(CASE WHEN exerciseResult.createdAt >= :today THEN exerciseResult.time_taken ELSE 0 END) AS todayTimeTaken',
        'SUM(CASE WHEN exerciseResult.createdAt >= :startOfWeek THEN exerciseResult.time_taken ELSE 0 END) AS weekTimeTaken',
        'SUM(CASE WHEN exerciseResult.createdAt >= :startOfMonth THEN exerciseResult.time_taken ELSE 0 END) AS monthTimeTaken',
        'SUM(CASE WHEN exerciseResult.createdAt >= :today THEN 1 ELSE 0 END) AS todayCount',
        'SUM(CASE WHEN exerciseResult.createdAt >= :startOfWeek THEN 1 ELSE 0 END) AS weekCount',
        'SUM(CASE WHEN exerciseResult.createdAt >= :startOfMonth THEN 1 ELSE 0 END) AS monthCount',
      ])
      .where('exerciseResult.child_id = :child_id', { child_id }) // Added WHERE condition for child_id
      .setParameters({
        today,
        startOfWeek,
        startOfMonth,
      })
      .getRawOne();

    return results;
  }
}
