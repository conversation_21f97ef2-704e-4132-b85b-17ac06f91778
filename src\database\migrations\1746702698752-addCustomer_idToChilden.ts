import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddCustomerIdToChilden1746702698752 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'children',
      new TableColumn({
        name: 'stripe_customer_id',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('children', 'stripe_customer_id');
  }
}
