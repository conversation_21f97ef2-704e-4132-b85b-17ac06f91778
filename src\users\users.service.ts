import { Injectable } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Not, Repository } from 'typeorm';
import { CreateUserInterface } from './interface/create-user.interface';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}
  async create(createUserInterface: CreateUserInterface) {
    const user = await this.userRepository.create(createUserInterface);
    return await this.userRepository.save(user);
  }

  async findAll(where: any) {
    if (!where.user_type) {
      where = { ...where, user_type: Not('super_admin') };
    }
    return await this.userRepository.find({
      where: { ...where },
    });
  }

  async findOne(id: string) {
    return await this.userRepository.findOne({
      where: { id },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    await this.userRepository.update(id, updateUserDto);
    return this.findOne(id);
  }

  async remove(id: string) {
    await this.userRepository.delete(id);
  }

  async findOneByParam(
    params: { [key: string]: any },
    relations?: string[],
  ): Promise<User | undefined> {
    // Find and return the record based on dynamic parameters
    // return this.userRepository.findOneBy(params);
    return this.userRepository.findOne({
      where: params,
      relations,
    });
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ) {
    const [users, total] = await this.userRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
    return [users, total];
  }

  async findWithRelations(
    params: { [key: string]: any },
    relations: string[],
  ): Promise<User | undefined> {
    // Find and return the record based on dynamic parameters
    return this.userRepository.findOne({
      where: params,
      relations,
    });
  }

  async updateEntity(user: User) {
    return await this.userRepository.save(user);
  }

  async findUserByEmail(email: string): Promise<User | null> {
    if (!email) {
      return null;
    }
    return await this.userRepository.findOne({ where: { email } });
  }

  async saveUser(user: User) {
    return await this.userRepository.save(user);
  }
}
