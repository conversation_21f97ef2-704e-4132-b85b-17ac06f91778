import {
  IsUUID,
  IsEnum,
  IsString,
  IsInt,
  IsOptional,
  IsDecimal,
  IsNotEmpty,
} from 'class-validator';

export class CreateExerciseResultDto {
  @IsNotEmpty()
  child_id: string;

  @IsNotEmpty()
  exercise_type_id: string;

  @IsNotEmpty()
  @IsEnum([
    'practice_exercise',
    'option_question',
    'downloadable_question',
    'hear_and_select',
    'speed_typing',
    'matching_game',
    'custom_game',
  ])
  reference_type: string;

  @IsString()
  @IsNotEmpty()
  reference_id: string;

  @IsInt()
  @IsOptional()
  time_taken: number;

  @IsEnum(['success', 'fail'])
  @IsOptional()
  result?: string;

  @IsDecimal()
  @IsOptional()
  score?: number;
}
