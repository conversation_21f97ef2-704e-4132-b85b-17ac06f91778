import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class Excerciseresults1746535710109 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'exercise_results',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'child_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'exercise_type_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'reference_type',
            type: 'enum',
            enum: [
              'practice_exercise',
              'option_question',
              'downloadable_question',
              'hear_and_select',
              'speed_typing',
              'matching_game',
              'custom_game',
            ],
            isNullable: false,
          },
          {
            name: 'reference_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'time_taken',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'result',
            type: 'enum',
            enum: ['success', 'fail'],
            isNullable: true,
          },
          {
            name: 'score',
            type: 'decimal',
            precision: 5,
            scale: 2,
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['child_id'],
            referencedTableName: 'children',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['exercise_type_id'],
            referencedTableName: 'exercise_types',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('exercise_results');
  }
}
