import {
  IsA<PERSON>y,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';
import { ExerciseEnum } from '../entities/exercise-type.entity';
import { Type } from 'class-transformer';

export class CreateExerciseTypeDto {
  @IsEnum(ExerciseEnum)
  @IsNotEmpty()
  exercise: ExerciseEnum;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  @Matches(/^[a-z]+$/, {
    message: 'identifier must be in lowercase and contain no spaces',
  })
  identifier: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Type(() => String)
  medical_condition_ids?: string[];

  @IsOptional()
  @IsBoolean()
  is_paid?: boolean;

  @IsOptional()
  @IsString()
  thumbnail?: string; //
}
