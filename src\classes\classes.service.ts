import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateClassDto } from './dto/create-class.dto';
import { UpdateClassDto } from './dto/update-class.dto';
import { Class } from './entities/class.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateClassInterface } from './interface/create-class.interface';

@Injectable()
export class ClassesService {
  constructor(
    @InjectRepository(Class)
    private classRepository: Repository<Class>,
  ) { }
  async create(createClassInterface: CreateClassInterface) {
    const newClass = this.classRepository.create(createClassInterface);
    return await this.classRepository.save(newClass);
  }

  async findAll() {
    return await this.classRepository.find({
      order: {
        class_name: 'ASC'
      }

    });
  }

  async findOne(id: string) {
    const cls = await this.classRepository.findOne({ where: { id } });
    if (!cls) {
      throw new NotFoundException(`Class #${id} not found`);
    }
    return cls;
  }

  async update(id: string, updateClassDto: UpdateClassDto) {
    await this.classRepository.update(id, updateClassDto);
    return this.findOne(id);
  }

  async remove(id: string) {
    return await this.classRepository.delete(id);
  }
}
