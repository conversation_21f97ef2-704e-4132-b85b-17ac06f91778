import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateExerciseDto } from './dto/create-exercise.dto';
import { UpdateExerciseDto } from './dto/update-exercise.dto';
import { Exercise } from './entities/exercise.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateExersiceInterface } from './interface/create-exersice.interface';
import { ExerciseMedicalCondition } from './entities/exersice-medical-condition.entity';
import { MedicalConditionService } from 'src/medical-condition/medical-condition.service';

@Injectable()
export class ExerciseService {
  constructor(
    @InjectRepository(Exercise)
    private readonly exerciseRepository: Repository<Exercise>,
    @InjectRepository(ExerciseMedicalCondition)
    private readonly exerciseMedicalConditionRepository: Repository<ExerciseMedicalCondition>,
    private readonly medicalConditionService: MedicalConditionService,
  ) {}

  async create(createExerciseDto: CreateExersiceInterface) {
    const newExersice = this.exerciseRepository.create(createExerciseDto);
    return this.exerciseRepository.save(newExersice);
  }

  async findAll(filter: any) {
    return await this.exerciseRepository.find();
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ) {
    const [Exercise, total] = await this.exerciseRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
    return [Exercise, total];
  }

  async findAllAsessmentExersise(age?: number) {
    const list = await this.findAll({});
    const assessmentExersice = list.filter(
      (exersice) => exersice.rules.for_assessment == true,
    );
    let updatedExersies = [];
    if (age) {
      for (const exersise of assessmentExersice) {
        if (exersise.rules.age_range.includes(age)) {
          updatedExersies.push(exersise);
        }
      }
    } else {
      updatedExersies = assessmentExersice;
    }
    return updatedExersies;
  }

  async findOne(id: string) {
    return await this.exerciseRepository.findOne({
      where: { id },
      relations: ['exerciseMedicalConditions.medicalCondition'],
    });
  }

  async update(id: string, updateExerciseDto: UpdateExerciseDto) {
    const exercise = await this.exerciseRepository.findOne({
      where: { id },
      relations: ['exerciseMedicalConditions'],
    });
    if (!exercise) {
      throw new NotFoundException(`Exercise with ID ${id} not found`);
    }
    const updatedExercise = Object.assign(exercise, updateExerciseDto);
    return await this.exerciseRepository.save(updatedExercise);
  }

  async remove(id: string) {
    return await this.exerciseRepository.delete(id);
  }

  async addMedicalConditionsToExercise(
    exercise: Exercise,
    medicalConditionIds: string[],
  ) {
    console.log(exercise.exerciseMedicalConditions);
    if (
      exercise.exerciseMedicalConditions &&
      exercise.exerciseMedicalConditions.length > 0
    ) {
      console.log('working');
      exercise.exerciseMedicalConditions.map(
        async (questionMedicalCondition) => {
          await this.exerciseMedicalConditionRepository.delete(
            questionMedicalCondition.id,
          );
        },
      );
    }
    for (const conditionId of medicalConditionIds) {
      const medicalCondition =
        await this.medicalConditionService.findOne(conditionId);

      if (!medicalCondition) {
        throw new NotFoundException(
          `Medical condition with ID ${conditionId} not found`,
        );
      }

      const exerciseMedicalConditions =
        this.exerciseMedicalConditionRepository.create({
          exercise,
          medicalCondition,
        });

      await this.exerciseMedicalConditionRepository.save(
        exerciseMedicalConditions,
      );
    }

    return this.exerciseRepository.findOne({
      where: { id: exercise.id },
      relations: [
        'exerciseMedicalConditions',
        'exerciseMedicalConditions.medicalCondition',
      ],
    });
  }
}
