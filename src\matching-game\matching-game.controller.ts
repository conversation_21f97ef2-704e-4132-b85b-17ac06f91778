import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { MatchingGameService } from './matching-game.service';
import { CreateMatchingGameDto } from './dto/create-matching-game.dto';
import { UpdateMatchingGameDto } from './dto/update-matching-game.dto';
import { ResponseService } from 'src/common/services/response.service';
import { ExerciseTypesService } from 'src/exercise-types/exercise-types.service';
import { ExerciseEnum } from 'src/exercise-types/entities/exercise-type.entity';
import { FindManyOptions } from 'typeorm';

@Controller('matching-game')
export class MatchingGameController {
  constructor(
    private readonly matchingGameService: MatchingGameService,
    private readonly responseService: ResponseService,
    private readonly exerciseTypeService: ExerciseTypesService,
  ) {}

  @Post()
  async create(@Body() createMatchingGameDto: CreateMatchingGameDto) {
    const question_type = await this.exerciseTypeService.findOne(
      createMatchingGameDto.exercise_type_id,
    );
    if (question_type.exercise !== ExerciseEnum.MATCHING_GAME) {
      throw new BadRequestException('Invalid Exercise Type');
    }
    const savedMatchingGame = await this.matchingGameService.create(
      createMatchingGameDto,
    );
    return this.responseService.successResponse(
      'New MatchingGame Created Successfully',
      savedMatchingGame,
    );
  }

  @Get()
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [matchingGames, total] =
      await this.matchingGameService.findAndCountByParam(where, skip, limit, [
        'exerciseType',
      ]);

    return this.responseService.successResponse('MatchingGame List', {
      data: matchingGames,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get('users')
  async findAllUser(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [matchingGames, total] =
      await this.matchingGameService.findAndCountByParam(where, skip, limit, [
        'exerciseType',
      ]);

    return this.responseService.successResponse('MatchingGame List', {
      data: matchingGames,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.matchingGameService.findOne(id);
    return this.responseService.successResponse('MatchingGame Details', data);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateMatchingGameDto: UpdateMatchingGameDto,
  ) {
    const updatedMatchingGame = await this.matchingGameService.update(
      id,
      updateMatchingGameDto,
    );
    return this.responseService.successResponse(
      'MatchingGame Updated Successfully',
      updatedMatchingGame,
    );
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.matchingGameService.remove(id);
    return this.responseService.successResponse(
      'MatchingGame Deleted Successfully',
    );
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.question_type_id) {
      where['question_type_id'] = filter.question_type_id;
    }
    if (filter.exercise_type_id) {
      where['exercise_type_id'] = filter.exercise_type_id;
    }
    if (filter.type) {
      where['type'] = filter.type;
    }
    return where;
  }
}
