import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>T<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { ExerciseType } from './exercise-type.entity';

@Entity('exercise_type_medical_conditions')
export class ExerciseTypeMedicalCondition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'exercise_type_id', type: 'varchar' })
  exercise_type_id: string;

  @Column({ name: 'medical_condition_id', type: 'varchar' })
  medical_condition_id: string;

  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.medicalConditions,
    {
      nullable: false,
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'exercise_type_id' })
  exerciseType: ExerciseType;

  @ManyToOne(() => MedicalCondition, { nullable: false, onDelete: 'CASCADE' })
  @Join<PERSON>olumn({ name: 'medical_condition_id' })
  medicalCondition: MedicalCondition;
}
