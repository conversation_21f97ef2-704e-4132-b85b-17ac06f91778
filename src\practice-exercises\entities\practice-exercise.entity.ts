import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  ManyToMany,
  JoinTable,
  OneToMany,
  JoinColumn,
  OneToOne,
} from 'typeorm';
import { PracticeExerciseLevel } from './practice-exercise-level.entity';
import { PracticeExerciseAge } from './practice-exercise-age.entity';
import { Transform } from 'class-transformer';

@Entity('practice_exercises')
export class PracticeExercise {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  description?: string; // Added description column

  @Column()
  image: string;

  @Column()
  reference_image: string;

  @Column()
  practice_exersise_type_id: string;

  @Column({ nullable: true })
  text: string;

  @Column({ nullable: true })
  thumbnail: string;

  @Column('int')
  time_limit: number;

  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  @Column('json')
  performance_percentage: any;

  @Column('decimal', { precision: 5, scale: 2 })
  trigger_score: number;

  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.practiceExercises,
  )
  @JoinColumn({ name: 'practice_exersise_type_id' })
  exerciseType: ExerciseType;

  @ManyToMany(() => MedicalCondition)
  @JoinTable({
    name: 'practice_exercises_medical_conditions',
    joinColumn: { name: 'practice_exersise_id', referencedColumnName: 'id' },
    inverseJoinColumn: {
      name: 'medical_condition_id',
      referencedColumnName: 'id',
    },
  })
  medicalConditions: MedicalCondition[];

  @OneToOne(() => PracticeExerciseLevel, { nullable: true })
  @JoinColumn({ name: 'level_id' })
  level: PracticeExerciseLevel;

  @OneToMany(() => PracticeExerciseAge, (age) => age.practiceExercise)
  ages: PracticeExerciseAge[];
}
