import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  JWT_ACCESS_EXPIRY,
  JWT_ACCESS_SECRET,
  JWT_REFRESH_EXPIRY,
  JWT_REFRESH_SECRET,
} from 'src/common/constants/jwt.constants';

@Injectable()
export class AuthTokenervice {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}
  // async generateTokens(userId: string, username: string) {
  //     const [accessToken, refreshToken] = await Promise.all([
  //         this.jwtService.signAsync(
  //             {
  //                 sub: userId,
  //                 username,
  //             },
  //             {
  //                 secret: this.configService.get<string>('JWT_ACCESS_SECRET'),
  //                 expiresIn:
  //                     this.configService.get<string>('JWT_ACCESS_EXPIRY'),
  //             },
  //         ),
  //         this.jwtService.signAsync(
  //             {
  //                 sub: userId,
  //                 username,
  //             },
  //             {
  //                 secret: this.configService.get<string>(
  //                     'JWT_REFRESH_SECRET',
  //                 ),
  //                 expiresIn:
  //                     this.configService.get<string>('JWT_REFRESH_EXPIRY'),
  //             },
  //         ),
  //     ]);

  //     return {
  //         accessToken,
  //         refreshToken,
  //     };
  // }

  async validatePasswordToken(passwordToken: string): Promise<string> {
    try {
      const decoded = await this.jwtService.verifyAsync(passwordToken, {
        secret: process.env.JWT_PASS_SECRET,
      });
      return decoded.sub; // return userId or relevant data from token
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired password token');
    }
  }

  async generatePassToken(userId: string) {
    const passwordToken = await this.jwtService.signAsync(
      {
        sub: userId,
      },
      {
        secret: process.env.JWT_PASS_SECRET,
        expiresIn: '10m',
      },
    );

    return passwordToken;
  }

  async generateTokens(userId: string, userName: string, userType: string) {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        {
          sub: userId,
          userName,
          userType,
        },
        {
          secret: process.env.JWT_ACCESS_SECRET,
          expiresIn: process.env.JWT_ACCESS_EXPIRY,
        },
      ),
      this.jwtService.signAsync(
        {
          sub: userId,
          userName,
        },
        {
          secret: process.env.JWT_REFRESH_SECRET,
          expiresIn: process.env.JWT_REFRESH_EXPIRY,
        },
      ),
    ]);

    return {
      type: 'Bearer',
      access_token: accessToken,
      refresh_token: refreshToken,
    };
  }
}
