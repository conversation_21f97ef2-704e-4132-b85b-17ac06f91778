import { Injectable } from '@nestjs/common';
import { CreateOptionQuestionDto } from './dto/create-option-question.dto';
import { UpdateOptionQuestionDto } from './dto/update-option-question.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, In, Repository } from 'typeorm';
import { OptionQuestionsAge } from './entities/option-questions-age.entity';
import { OptionQuestionsMedicalConditions } from './entities/option-question-medical-condition.entity';
import { OptionQuestion } from './entities/option-question.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { CreateOptionQuestionInterface } from './interface/create-question.interface';
import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';
import { UpdateOptionQuestionInterface } from './interface/update-option-question.interface';
import { OptionQuestionLevel } from './entities/option-questions-level.entity';
import { HearAndSelect } from 'src/hear-and-select/entities/hear-and-select.entity';

@Injectable()
export class OptionQuestionsService {
  constructor(
    @InjectRepository(OptionQuestion)
    private readonly optionQuestionRepository: Repository<OptionQuestion>,

    @InjectRepository(OptionQuestionsMedicalConditions)
    private readonly optionQuestionsMedicalConditionsRepository: Repository<OptionQuestionsMedicalConditions>,

    @InjectRepository(OptionQuestionsAge)
    private readonly optionQuestionsAgeRepository: Repository<OptionQuestionsAge>,

    @InjectRepository(OptionQuestionLevel)
    private readonly optionQuestionsLevelsRepository: Repository<OptionQuestionLevel>,
  ) {}

  async create(
    createOptionQuestionInterface: CreateOptionQuestionInterface,
  ): Promise<OptionQuestion> {
    const newOptionQuestion = this.optionQuestionRepository.create(
      createOptionQuestionInterface,
    );
    return await this.optionQuestionRepository.save(newOptionQuestion);
  }

  // Get all OptionQuestion
  async findAll(filter: any) {
    const page = parseInt(filter.page) || 1; // Default to page 1
    const limit = parseInt(filter.limit) || 10; // Default to 10 items per page
    const skip = (page - 1) * limit;
    let where: FindManyOptions['where'] = {};

    if (filter.time_limit) {
      where = { time_limit: filter.time_limit };
    }

    if (filter.trigger_score) {
      where = { ...where, trigger_score: filter.trigger_score };
    }

    if (filter.ages) {
      where = { ...where, ages: { age: In(filter.ages) } };
    }

    if (filter.exercise_type_id) {
      where = {
        ...where,
        exercise_type_id: filter.exercise_type_id,
      };
    }
    if (filter.exercise_type_identifier) {
      where = {
        ...where,
        exerciseType: { identifier: filter.exercise_type_identifier },
      };
    }
    const [exerciseList, total] =
      await this.optionQuestionRepository.findAndCount({
        where,
        relations: ['exerciseType'],
        skip,
        take: limit,
      });
    return [exerciseList, total];
  }

  // Get a single OptionQuestion by ID
  async findOne(id: string): Promise<OptionQuestion> {
    return await this.optionQuestionRepository.findOne({
      where: { id },
      relations: ['exerciseType', 'ages'],
    });
  }

  async findByParam(params: FindManyOptions['where'], relations?: string[]) {
    return this.optionQuestionRepository.find({
      where: params,
      order: { level: { level: 'ASC' } },
      relations,
    });
  }

  // Update an OptionQuestion
  async update(
    existingOptionQuestion: OptionQuestion,
    updateOptionQuestionInterface: UpdateOptionQuestionInterface,
  ): Promise<OptionQuestion> {
    // await this.optionQuestionRepository.update(id, updateOptionQuestionDto);
    // return this.findOne(id);
    existingOptionQuestion.question_image =
      updateOptionQuestionInterface.question_image
        ? updateOptionQuestionInterface.question_image
        : existingOptionQuestion.question_image;
    existingOptionQuestion.option_1_image =
      updateOptionQuestionInterface.option_1_image
        ? updateOptionQuestionInterface.option_1_image
        : existingOptionQuestion.option_1_image;
    existingOptionQuestion.option_2_image =
      updateOptionQuestionInterface.option_2_image
        ? updateOptionQuestionInterface.option_2_image
        : existingOptionQuestion.option_2_image;
    existingOptionQuestion.option_3_image =
      updateOptionQuestionInterface.option_3_image
        ? updateOptionQuestionInterface.option_3_image
        : existingOptionQuestion.option_3_image;
    existingOptionQuestion.option_4_image =
      updateOptionQuestionInterface.option_4_image
        ? updateOptionQuestionInterface.option_4_image
        : existingOptionQuestion.option_4_image;
    existingOptionQuestion.option_5_image =
      updateOptionQuestionInterface.option_5_image
        ? updateOptionQuestionInterface.option_5_image
        : existingOptionQuestion.option_5_image;
    existingOptionQuestion.correct_options =
      updateOptionQuestionInterface.correct_options
        ? updateOptionQuestionInterface.correct_options
        : existingOptionQuestion.correct_options;
    existingOptionQuestion.time_limit = updateOptionQuestionInterface.time_limit
      ? updateOptionQuestionInterface.time_limit
      : existingOptionQuestion.time_limit;
    existingOptionQuestion.answer_description =
      updateOptionQuestionInterface.answer_description
        ? updateOptionQuestionInterface.answer_description
        : existingOptionQuestion.answer_description;
    existingOptionQuestion.exerciseType =
      updateOptionQuestionInterface.exerciseType
        ? updateOptionQuestionInterface.exerciseType
        : existingOptionQuestion.exerciseType;
    existingOptionQuestion.text = updateOptionQuestionInterface.text
      ? updateOptionQuestionInterface.text
      : existingOptionQuestion.text;
    existingOptionQuestion.description =
      updateOptionQuestionInterface.description
        ? updateOptionQuestionInterface.description
        : existingOptionQuestion.description;

    return await this.optionQuestionRepository.save(existingOptionQuestion);
  }

  // Delete an OptionQuestion
  async remove(id: string): Promise<void> {
    await this.optionQuestionRepository.delete(id);
  }

  // Associate a medical condition with an OptionQuestion
  // async addMedicalConditions(
  //   optionQuestionId: string,
  //   medicalConditions: MedicalCondition[],
  // ): Promise<OptionQuestionsMedicalConditions[]> {
  //   const optionQuestion = await this.optionQuestionRepository.findOne({
  //     where: { id: optionQuestionId },
  //   });
  //   if (!optionQuestion) {
  //     throw new Error('OptionQuestion not found');
  //   }
  //   const OptionQuestionMedicalConditions = medicalConditions.map(
  //     (medicalCondition) => {
  //       return this.optionQuestionsMedicalConditionsRepository.create({
  //         optionQuestion,
  //         medicalCondition: medicalCondition,
  //       });
  //     },
  //   );
  //   return await this.optionQuestionsMedicalConditionsRepository.save(
  //     OptionQuestionMedicalConditions,
  //   );
  // }

  //updateing ranks
  // async checkAndUpdateRankList(
  //   level: number,
  //   exerciseType: ExerciseType,
  //   optionQuestion: OptionQuestion,
  // ) {
  //   const levelExist = await this.optionQuestionsLevelsRepository.findOne({
  //     where: { level, exerciseType },
  //     relations: ['optionQuestion'],
  //   });

  //   //console.log(levelExist);

  //   let savedlevel;

  //   if (levelExist) {
  //     if (levelExist.optionQuestion) {
  //       const existingLevels = await this.optionQuestionsLevelsRepository.find({
  //         where: { exerciseType },
  //         select: ['level'],
  //         order: { level: 'ASC' },
  //       });
  //       const levelsArray = existingLevels.map((levelObj) => levelObj.level);
  //       const missingLevels = [];
  //       for (let i = 1; i < levelsArray.length; i++) {
  //         if (levelsArray[i] !== levelsArray[i - 1] + 1) {
  //           for (let j = levelsArray[i - 1] + 1; j < levelsArray[i]; j++) {
  //             missingLevels.push(j);
  //           }
  //         }
  //       }
  //       await this.optionQuestionRepository
  //         .createQueryBuilder()
  //         .update(OptionQuestionLevel)
  //         .set({ level: () => 'level + 1' })
  //         .where('exersise_type_id = :exerciseTypeId', {
  //           exerciseTypeId: exerciseType.id,
  //         })
  //         .andWhere('level >= :level', { level })
  //         .execute();
  //       const newLevel = this.optionQuestionsLevelsRepository.create({
  //         exerciseType,
  //         level,
  //       });
  //       savedlevel = await this.optionQuestionsLevelsRepository.save(newLevel);
  //     } else {
  //       savedlevel = levelExist;
  //     }
  //   } else {
  //     const newLevel = this.optionQuestionsLevelsRepository.create({
  //       exerciseType,
  //       level,
  //     });
  //     savedlevel = await this.optionQuestionsLevelsRepository.save(newLevel);
  //   }
  //   // console.log(savedlevel);
  //   optionQuestion.level = savedlevel;
  //   await this.optionQuestionRepository.save(optionQuestion);
  // }

  //Add Age to Option Question
  async addAgeToOptionQuestion(optionQuestion: OptionQuestion, ages: number[]) {
    await this.optionQuestionsAgeRepository.delete({
      option_questions_id: optionQuestion.id,
    });
    const ageEntities = ages.map((age) => {
      const ageEntity = new OptionQuestionsAge();
      ageEntity.age = age;
      ageEntity.option_questions_id = optionQuestion.id;
      return ageEntity;
    });
    return await this.optionQuestionsAgeRepository.save(ageEntities);
  }

  // async updateMedicalConditions(
  //   optionQuestionId: string,
  //   medicalConditions: MedicalCondition[],
  // ): Promise<OptionQuestionsMedicalConditions[]> {
  //   // Remove existing medical conditions
  //   await this.optionQuestionsMedicalConditionsRepository.delete({
  //     optionQuestion: { id: optionQuestionId },
  //   });

  //   // Add new medcal conditons
  //   const optionQuestionMedicalConditions = medicalConditions.map(
  //     (medicalCondition) => {
  //       return this.optionQuestionsMedicalConditionsRepository.create({
  //         optionQuestion: { id: optionQuestionId },
  //         medicalCondition,
  //       });
  //     },
  //   );

  //   return await this.optionQuestionsMedicalConditionsRepository.save(
  //     optionQuestionMedicalConditions,
  //   );
  // }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[OptionQuestion[], number]> {
    const [questionTypes, total] =
      await this.optionQuestionRepository.findAndCount({
        where: params,
        relations,
        skip,
        take,
      });
    return [questionTypes, total];
  }
}
