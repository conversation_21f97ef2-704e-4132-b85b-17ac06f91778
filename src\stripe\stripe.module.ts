import { DynamicModule, forwardRef, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { StripeController } from './stripe.controller';
import { StripeService } from './stripe.service';
import { UsersModule } from 'src/users/users.module';
import { TransationsModule } from 'src/transations/transations.module';
import { PlansModule } from 'src/plans/plans.module';
import { CommonModule } from 'src/common/common.module';
import { AssessmentsModule } from 'src/assessments/assessments.module';
import { WebhookController } from './webhook.controller';
import { StripeSubscriptionService } from './services/stripe-subscription.service';
import { SubscriptionsModule } from 'src/subscriptions/subscriptions.module';

@Module({})
export class StripeModule {
  static forRootAsync(): DynamicModule {
    return {
      module: StripeModule,
      controllers: [StripeController, WebhookController],
      imports: [
        ConfigModule.forRoot(),
        UsersModule,
        forwardRef(() => TransationsModule),
        PlansModule,
        CommonModule,
        AssessmentsModule,
        SubscriptionsModule,
      ],
      providers: [
        StripeService,
        StripeSubscriptionService,
        {
          provide: 'STRIPE_API_KEY',
          useFactory: async (configService: ConfigService) =>
            configService.get('STRIPE_API_KEY'),
          inject: [ConfigService],
        },
      ],
      exports: [StripeService, StripeSubscriptionService],
    };
  }
}
