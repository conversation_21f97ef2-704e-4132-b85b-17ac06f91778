import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToOne,
} from 'typeorm';
import { PracticeExercise } from './practice-exercise.entity';

@Entity('practice_exercises_levels')
export class PracticeExerciseLevel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ExerciseType, (exerciseType) => exerciseType.levels)
  @JoinColumn({ name: 'practice_exercise_type_id' })
  practiceExerciseType: ExerciseType;

  @Column()
  level: number;

  @OneToOne(
    () => PracticeExercise,
    (practiceExercise) => practiceExercise.level,
  )
  practiceExercise: PracticeExercise;
}
