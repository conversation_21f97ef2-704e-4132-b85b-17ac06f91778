import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class DropDownloadableQuestionTypesTable1732550100345
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('downloadable_question_types');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'downloadable_question_types',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'identifier',
            type: 'varchar',
            isNullable: true,
          },
        ],
      }),
    );
  }
}
