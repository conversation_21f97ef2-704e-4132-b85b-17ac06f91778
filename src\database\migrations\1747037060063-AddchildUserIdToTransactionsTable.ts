import {
  MigrationInterface,
  QueryRunner,
  Table<PERSON><PERSON>umn,
  TableForeignKey,
} from 'typeorm';

export class AddchildUserIdToTransactionsTable1747037060063
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add 'child_user_id' column
    await queryRunner.addColumn(
      'transactions',
      new TableColumn({
        name: 'child_id',
        type: 'varchar',
        isNullable: true,
        length: '36',
      }),
    );

    // Add 'plan_price_id' column
    await queryRunner.addColumn(
      'transactions',
      new TableColumn({
        name: 'plan_price_id',
        type: 'varchar',
        isNullable: true,
        length: '36',
      }),
    );

    // Add foreign key for 'child_user_id'
    await queryRunner.createForeignKey(
      'transactions',
      new TableForeignKey({
        columnNames: ['child_id'],
        referencedTableName: 'children',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );

    // Add foreign key for 'plan_price_id'
    await queryRunner.createForeignKey(
      'transactions',
      new TableForeignKey({
        columnNames: ['plan_price_id'],
        referencedTableName: 'plan_prices',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key for 'plan_price_id'
    const table = await queryRunner.getTable('transactions');
    const planPriceForeignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('plan_price_id') !== -1,
    );
    if (planPriceForeignKey) {
      await queryRunner.dropForeignKey('transactions', planPriceForeignKey);
    }

    // Drop foreign key for 'child_user_id'
    const childUserForeignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('child_user_id') !== -1,
    );
    if (childUserForeignKey) {
      await queryRunner.dropForeignKey('transactions', childUserForeignKey);
    }

    // Drop 'plan_price_id' column
    await queryRunner.dropColumn('transactions', 'plan_price_id');

    // Drop 'child_user_id' column
    await queryRunner.dropColumn('transactions', 'child_user_id');
  }
}
