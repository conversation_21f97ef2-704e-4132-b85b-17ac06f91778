import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateDownloadableQuestionDto } from './dto/create-downloadable-question.dto';
import { UpdateDownloadableQuestionDto } from './dto/update-downloadable-question.dto';
import { DownloadableQuestion } from './entities/downloadable-question.entity';
import { DownloadableQuestionsAge } from './entities/downloadable-questions-age.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, Repository } from 'typeorm';
import { DownloadableQuestionsLevel } from './entities/downloadable-questions-level.entity';
import { DownloadableQuestionsMedicalCondition } from './entities/downloadable-questions-medical-conditions.entity';
import { CreateDownloadableQuestionInterface } from './interface/create-downloadable-questions.interface';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { String } from 'aws-sdk/clients/batch';
import { UpdateDownloadableQuestionInterface } from './interface/update-downloadable-questions.interface';
import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';

@Injectable()
export class DownloadableQuestionsService {
  constructor(
    @InjectRepository(DownloadableQuestion)
    private readonly downloadableQuestionsRepository: Repository<DownloadableQuestion>,
    @InjectRepository(DownloadableQuestionsAge)
    private readonly downloadableQuestionsAgeRepository: Repository<DownloadableQuestionsAge>,
    @InjectRepository(DownloadableQuestionsLevel)
    private readonly downloadableQuestionsLevelRepository: Repository<DownloadableQuestionsLevel>,
    @InjectRepository(DownloadableQuestionsMedicalCondition)
    private readonly downloadableQuestionsMedicalConditionsRepository: Repository<DownloadableQuestionsMedicalCondition>,
  ) {}
  async create(
    createDownloadableQuestionInterface: CreateDownloadableQuestionInterface,
  ) {
    const newDownlaodQuestion = this.downloadableQuestionsRepository.create(
      createDownloadableQuestionInterface,
    );
    return await this.downloadableQuestionsRepository.save(newDownlaodQuestion);
  }

  // Find all Downloadable Question Types with optional filters
  async findAll(params: {
    [key: string]: any;
  }): Promise<DownloadableQuestion[]> {
    return await this.downloadableQuestionsRepository.find({
      where: params,
      relations: ['exerciseType'],
    });
  }

  // Find all Downloadable Questions with Params
  async findByParam(params: FindManyOptions['where'], relations?: string[]) {
    return this.downloadableQuestionsRepository.find({
      where: params,
      order: { level: { level: 'ASC' } },
      relations,
    });
  }

  // Find and count paginated Downloadable Question Types
  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[DownloadableQuestion[], number]> {
    const [questionTypes, total] =
      await this.downloadableQuestionsRepository.findAndCount({
        where: params,
        relations,
        skip,
        take,
      });
    return [questionTypes, total];
  }

  // Find and return the record based on dynamic parameters
  async findOneByParam(
    params: { [key: string]: any },
    relations?: string[],
  ): Promise<DownloadableQuestion | undefined> {
    return this.downloadableQuestionsRepository.findOne({
      where: params,
      relations,
    });
  }

  // Find a single Downloadable Question Type by ID
  async findOne(id: string): Promise<DownloadableQuestion> {
    const questionType = await this.downloadableQuestionsRepository.findOne({
      where: { id },
      relations: ['exerciseType', 'questionAges'],
    });
    if (!questionType) {
      throw new NotFoundException(
        `Downloadable Question with ID ${id} not found`,
      );
    }
    return questionType;
  }

  //Update a downloadable Question
  async update(
    existingDownloadableQuestion: DownloadableQuestion,
    updateDownloadableQuestionInterface: UpdateDownloadableQuestionInterface,
  ) {
    existingDownloadableQuestion.description =
      updateDownloadableQuestionInterface.description
        ? updateDownloadableQuestionInterface.description
        : existingDownloadableQuestion.description;
    existingDownloadableQuestion.question_file =
      updateDownloadableQuestionInterface.question_file
        ? updateDownloadableQuestionInterface.question_file
        : existingDownloadableQuestion.question_file;
    existingDownloadableQuestion.exercise_type_id =
      updateDownloadableQuestionInterface.exercise_type_id
        ? updateDownloadableQuestionInterface.exercise_type_id
        : existingDownloadableQuestion.exercise_type_id;
    return await this.downloadableQuestionsRepository.save(
      existingDownloadableQuestion,
    );
  }

  // Remove a Downloadable Question by ID
  async remove(id: string): Promise<void> {
    const question = await this.findOne(id);

    await this.downloadableQuestionsRepository.remove(question);
  }

  //Add Age to Downlodable Question
  async addAgeToDownladableQuestion(
    downloadableQuestion: DownloadableQuestion,
    ages: number[],
  ) {
    const ageEntities = ages.map((age) => {
      const ageEntity = new DownloadableQuestionsAge();
      ageEntity.age = age;
      ageEntity.downloadable_questions_id = downloadableQuestion.id;
      return ageEntity;
    });
    return await this.downloadableQuestionsAgeRepository.save(ageEntities);
  }

  // Associate a medical condition with an DownloadableQuestion
  // async addMedicalConditions(
  //   downloadQuestionId: string,
  //   medicalConditions: MedicalCondition[],
  // ) {
  //   const downloadableQuestion =
  //     await this.downloadableQuestionsRepository.findOne({
  //       where: { id: downloadQuestionId },
  //     });
  //   if (!downloadableQuestion) {
  //     throw new Error('Downloadable Question not found');
  //   }
  //   const downloadableQuestionMedicalConditions = medicalConditions.map(
  //     (medicalCondition) => {
  //       return this.downloadableQuestionsMedicalConditionsRepository.create({
  //         downloadableQuestion,
  //         medicalCondition: medicalCondition,
  //       });
  //     },
  //   );
  //   await this.downloadableQuestionsMedicalConditionsRepository.save(
  //     downloadableQuestionMedicalConditions,
  //   );
  //   return medicalConditions;
  // }

  // //updateing ranks
  // async checkAndUpdateRankList(
  //   level: number,
  //   exerciseType: ExerciseType,
  //   downloadbleQuestion: DownloadableQuestion,
  // ) {
  //   const levelExist = await this.downloadableQuestionsLevelRepository.findOne({
  //     where: { level, exerciseType },
  //     relations: ['downloadableQuestion'],
  //   });
  //   //  console.log(levelExist);
  //   let savedlevel;
  //   if (levelExist) {
  //     if (levelExist.downloadableQuestion) {
  //       const existingLevels =
  //         await this.downloadableQuestionsLevelRepository.find({
  //           where: { exerciseType },
  //           select: ['level'],
  //           order: { level: 'ASC' },
  //         });
  //       const levelsArray = existingLevels.map((levelObj) => levelObj.level);
  //       const missingLevels = [];
  //       for (let i = 1; i < levelsArray.length; i++) {
  //         if (levelsArray[i] !== levelsArray[i - 1] + 1) {
  //           for (let j = levelsArray[i - 1] + 1; j < levelsArray[i]; j++) {
  //             missingLevels.push(j);
  //           }
  //         }
  //       }
  //       await this.downloadableQuestionsRepository
  //         .createQueryBuilder()
  //         .update(DownloadableQuestionsLevel)
  //         .set({ level: () => 'level + 1' })
  //         .where('exercise_type_id = :exerciseTypeId', {
  //           exerciseTypeId: exerciseType.id,
  //         })
  //         .andWhere('level >= :level', { level })
  //         .execute();
  //       const newLevel = this.downloadableQuestionsLevelRepository.create({
  //         exercise_type_id: exerciseType.id,
  //         level,
  //       });

  //       savedlevel =
  //         await this.downloadableQuestionsLevelRepository.save(newLevel);
  //     } else {
  //       savedlevel = levelExist;
  //     }
  //   } else {
  //     const newLevel = this.downloadableQuestionsLevelRepository.create({
  //       exercise_type_id: exerciseType.id,
  //       level,
  //     });
  //     savedlevel =
  //       await this.downloadableQuestionsLevelRepository.save(newLevel);
  //   }

  //   downloadbleQuestion.level = savedlevel;
  //   delete downloadbleQuestion.questionAges;
  //   delete downloadbleQuestion.medicalConditions;
  //   //console.log(downloadbleQuestion);
  //   return await this.downloadableQuestionsRepository.save(downloadbleQuestion);
  // }

  async updateAgesForDownloadableQuestion(
    downloadableQuestion: DownloadableQuestion,
    ages: number[],
  ): Promise<DownloadableQuestionsAge[]> {
    await this.downloadableQuestionsAgeRepository.delete({
      downloadableQuestion: { id: downloadableQuestion.id },
    });
    const ageEntities = ages.map((age) => {
      const ageEntity = new DownloadableQuestionsAge();
      ageEntity.age = age;
      ageEntity.downloadable_questions_id = downloadableQuestion.id;
      return ageEntity;
    });

    return await this.downloadableQuestionsAgeRepository.save(ageEntities);
  }
  // questionType;
  // async updateMedicalConditions(
  //   downloadableQuestionId: string,
  //   medicalConditions: MedicalCondition[],
  // ) {
  //   await this.downloadableQuestionsMedicalConditionsRepository.delete({
  //     downloadableQuestion: { id: downloadableQuestionId },
  //   });

  //   const downloadableQuestionMedicalConditions = medicalConditions.map(
  //     (medicalCondition) => {
  //       return this.downloadableQuestionsMedicalConditionsRepository.create({
  //         downloadableQuestion: { id: downloadableQuestionId },
  //         medicalCondition,
  //       });
  //     },
  //   );
  //   await this.downloadableQuestionsMedicalConditionsRepository.save(
  //     downloadableQuestionMedicalConditions,
  //   );
  //   return medicalConditions;
  // }
}
