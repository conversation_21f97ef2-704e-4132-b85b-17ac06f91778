import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateExerciseTable1730876482190 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('exercise', 'shape');
    await queryRunner.dropColumn('exercise', 'type');
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'type',
        type: 'enum',
        enum: [
          'word_tracing',
          'image_tracing',
          'drawing',
          'puzzle',
          'route_mapping',
          'similar_figures',
        ],
        isNullable: false,
      }),
    );
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'image',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'reference_image',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'rules',
        type: 'json',
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'trigger_score',
        type: 'decimal',
        precision: 10,
        scale: 2,
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'created_at',
        type: 'timestamp',
        default: 'CURRENT_TIMESTAMP',
      }),
    );
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'updated_at',
        type: 'timestamp',
        default: 'CURRENT_TIMESTAMP',
        onUpdate: 'CURRENT_TIMESTAMP',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('exercise', 'type');
    await queryRunner.dropColumn('exercise', 'image');
    await queryRunner.dropColumn('exercise', 'reference_image');
    await queryRunner.dropColumn('exercise', 'rules');
    await queryRunner.dropColumn('exercise', 'trigger_score');
    await queryRunner.dropColumn('exercise', 'created_at');
    await queryRunner.dropColumn('exercise', 'updated_at');
    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'type',
        type: 'enum',
        enum: [
          'tracing',
          'drawing',
          'puzzle',
          'route_mapping',
          'similar_figures',
        ],
        isNullable: false,
      }),
    );

    await queryRunner.addColumn(
      'exercise',
      new TableColumn({
        name: 'shape',
        type: 'varchar',
        length: '255',
        isNullable: false,
      }),
    );
  }
}
