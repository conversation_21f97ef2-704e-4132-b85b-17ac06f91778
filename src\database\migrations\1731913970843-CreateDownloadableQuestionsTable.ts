import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateDownloadableQuestionsTable1731913970843
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'downloadable_questions',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'question_file',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'question_type_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'description',
            type: 'longtext',
            isNullable: true,
          },
        ],
      }),
    );

    await queryRunner.createForeignKey(
      'downloadable_questions',
      new TableForeignKey({
        columnNames: ['question_type_id'],
        referencedTableName: 'downloadable_question_types',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('downloadable_questions');
  }
}
