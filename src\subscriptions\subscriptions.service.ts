import {
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import {
  Subscription,
  SubscriptionStatus,
} from './entities/subscription.entity';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { ResponseService } from 'src/common/services/response.service';
import { PlanPricesService } from 'src/plan-prices/plan-prices.service';
import { StripeService } from 'src/stripe/stripe.service';
import { StripeSubscriptionService } from 'src/stripe/services/stripe-subscription.service';
import { ChildrenService } from 'src/children/children.service';

@Injectable()
export class SubscriptionsService {
  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    private readonly responseService: ResponseService,
    private readonly planPricesService: PlanPricesService,
    @Inject(forwardRef(() => StripeSubscriptionService))
    private readonly stripeSubscriptionService: StripeSubscriptionService,
    private readonly childrenService: ChildrenService,
  ) {}

  async create(data: Partial<Subscription>) {
    const newSubscription = this.subscriptionRepository.create(data);
    return await this.subscriptionRepository.save(newSubscription);
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[Subscription[], number]> {
    return await this.subscriptionRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
  }

  async cancelSubscription(id: string, cancellationDate: Date) {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id },
    });
    if (!subscription) {
      throw new Error('Subscription not found');
    }
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelled_date = cancellationDate;
    subscription.cancellation_effective_date = cancellationDate;
    return await this.subscriptionRepository.save(subscription);
  }

  async createStripeSection(
    createSubscriptionDto: CreateSubscriptionDto,
    user_id: string,
  ) {
    // return await this.stripeSubscriptionService.createProduct("test");
    // return await this.stripeSubscriptionService.createPrice(100,"prod_SITu7yNnExd0c5");

    const { child_id, plan_id, plan_price_id, total_count, billing_address } =
      createSubscriptionDto;
    const period = total_count ?? 12;
    const child = await this.childrenService.findOneByParam({ id: child_id }, [
      'user',
    ]);
    if (!child) {
      throw new NotFoundException({
        status: false,
        message: 'Child not found',
      });
    }
    //save billing_address to child
    child.billing_address = billing_address;
    await this.childrenService.saveInstance(child);

    const plan_price = await this.planPricesService.findOneByParam(
      { id: plan_price_id },
      ['plan'],
    );

    if (plan_price.stripe_price_id === null) {
      throw new ConflictException({
        status: false,
        message: 'Plan not present in stripe',
      });
    }
    const subscription = await this.subscriptionRepository.findOne({
      where: {
        user_id: child.user_id,
        child_id: child_id,
        status: In(['trialing', 'active', 'incomplete', 'past_due']),
      },
    });
    if (subscription) {
      throw new UnprocessableEntityException({
        status: false,
        message: `You have already subscribed to a plan. You have the option to cancel your current plan and subscribe to a new one, or you can choose to change your current plan`,
      });
    }
    const session = await this.stripeSubscriptionService.createSection(
      plan_price.plan,
      plan_price,
      child,
      child.user,
      createSubscriptionDto,
    );
    // console.log(session, 'session');
    if (!session) {
      throw new ConflictException({
        status: false,
        message: 'something went wrong please try again',
      });
    }
    const result = {
      url: session.url,
      id: session.id,
      success_url: session.success_url,
      cancel_url: session.cancel_url,
    };
    return this.responseService.successResponse('Session created', result);
  }

  async initiateInstance() {
    return new Subscription();
  }

  async saveInstance(subscription: Subscription): Promise<Subscription> {
    return await this.subscriptionRepository.save(subscription);
  }

  async findOneByParam(
    params: { [key: string]: any },
    relations?: string[],
  ): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.findOne({
      where: params,
      relations,
    });

    return subscription;
  }

  async findActiveSubscriptions(child_id: string) {
    const subscription = await this.subscriptionRepository.findOne({
      where: {
        child_id: child_id,
        status: In(['trialing', 'active', 'incomplete', 'past_due']),
      },
    });

    return {
      is_active: subscription ? true : false,
      subscription: subscription,
    };
  }
}
