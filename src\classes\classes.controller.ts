import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { ClassesService } from './classes.service';
import { CreateClassDto } from './dto/create-class.dto';
import { UpdateClassDto } from './dto/update-class.dto';
import { ResponseService } from 'src/common/services/response.service';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';

@Controller('classes')
export class ClassesController {
  constructor(
    private readonly classesService: ClassesService,
    private readonly responseService: ResponseService,
  ) {}

  @UseGuards(AccessTokenGuard)
  @Post()
  async create(@Body() createClassDto: CreateClassDto) {
    const newclass = await this.classesService.create(createClassDto);
    return this.responseService.successResponse('New Class Created', newclass);
  }

  @Get()
  async findAll() {
    const classList = await this.classesService.findAll();
    return this.responseService.successResponse('Classes List', classList);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.classesService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateClassDto: UpdateClassDto,
  ) {
    const updatedData = await this.classesService.update(id, updateClassDto);
    return this.responseService.successResponse(
      'class updated sucessfully',
      updatedData,
    );
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.classesService.findOne(id);
    await this.classesService.remove(id);
    return this.responseService.successResponse('Class deleted sucessfully');
  }
}
