import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsArray,
  IsBoolean,
  IsInt,
  Min,
  Max,
  ValidateNested,
  ArrayNotEmpty,
  IsIn,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';

class RulesDto {
  @IsBoolean()
  @IsNotEmpty()
  medical_question: boolean;

  @IsBoolean()
  @IsNotEmpty()
  allow_multiple: boolean;

  @IsInt()
  @IsNotEmpty()
  @Min(1)
  max_options: number;

  @IsInt()
  @Min(0)
  min_age: number;

  @IsInt()
  @Min(0)
  max_age: number;
}

export class CreateQuestionDto {
  @IsString()
  @IsNotEmpty()
  question_text: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  options: string[];

  @ValidateNested()
  @Type(() => RulesDto)
  @IsNotEmpty()
  rules: RulesDto;

  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  triggering_answers: string[];

  @IsArray()
  @IsUUID('all', { each: true })
  @IsOptional()
  medical_condition_ids: string[];
}
