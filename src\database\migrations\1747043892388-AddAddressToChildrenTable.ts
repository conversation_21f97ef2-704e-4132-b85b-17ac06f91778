import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddAddressToChildrenTable1747043892388
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'children',
      new TableColumn({
        name: 'billing_address',
        type: 'json',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('children', 'billing_address');
  }
}
