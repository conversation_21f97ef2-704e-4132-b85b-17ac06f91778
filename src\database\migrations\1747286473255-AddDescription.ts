import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDescription1747286473255 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'option_questions',
      new TableColumn({
        name: 'description',
        type: 'varchar',
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      'practice_exercises',
      new TableColumn({
        name: 'description',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('option_questions', 'description');
    await queryRunner.dropColumn('practice_exercises', 'description');
  }
}
