import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  UseGuards,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { ChildrenService } from './children.service';
import { CreateChildDto } from './dto/create-child.dto';
import { UpdateChildDto } from './dto/update-child.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Role } from 'src/common/decorators/roles.decorator';
import { ResponseService } from 'src/common/services/response.service';
import { ClassesService } from 'src/classes/classes.service';
import { CreateChildByAdminDto } from './dto/create-child-admin.dto,';
import { UsersService } from 'src/users/users.service';
import { FindManyOptions, Like } from 'typeorm';

@Controller('children')
export class ChildrenController {
  constructor(
    private readonly childrenService: ChildrenService,
    private readonly responseService: ResponseService,
    private readonly classesService: ClassesService,
    private readonly usersService: UsersService,
  ) {}

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post()
  async createChildtoUser(
    @Body() createChildByAdminDto: CreateChildByAdminDto,
  ) {
    const user = await this.usersService.findOne(createChildByAdminDto.user_id);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const clas = await this.classesService.findOne(
      createChildByAdminDto.class_id,
    );
    const newChild = await this.childrenService.create({
      ...createChildByAdminDto,
      user,
      class: clas,
    });
    return this.responseService.successResponse(
      'Child assigned successfully',
      newChild,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('teacher', 'parent')
  @Post('assign')
  async create(@Body() createChildDto: CreateChildDto, @Request() req) {
    const user_id = req.user.sub;
    const clas = await this.classesService.findOne(createChildDto.class_id);
    const newChild = await this.childrenService.create({
      ...createChildDto,
      user_id,
      class: clas,
    });
    return this.responseService.successResponse(
      'Child assigned successfully',
      newChild,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll(@Query() filter, @Request() req) {
    let user_type = req.user.userType;
    let params: any = {};
    if (filter.user_id) {
      params.user_id = filter.user_id;
    }
    if (user_type !== 'super_admin') {
      params.user_id = req.user.sub;
    }
    if (filter.name) {
      params.name = Like(`%${filter.name}%`);
    }
    const studentList = await this.childrenService.findByParam(params);
    return this.responseService.successResponse('Student List', studentList);
  }

  @UseGuards(AccessTokenGuard)
  @Get('paginated')
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page) || 1;
    const limit = parseInt(filter.limit) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [children, total] = await this.childrenService.findAndCountByParam(
      where,
      skip,
      limit,
    );

    return this.responseService.successResponse('Children List', {
      children: children,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @UseGuards(AccessTokenGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.childrenService.findOne(id);
  }

  @UseGuards(AccessTokenGuard)
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateChildDto: UpdateChildDto,
  ) {
    const updatedChild = await this.childrenService.update(id, updateChildDto);
    return this.responseService.successResponse(
      'Child Updated Sucessfully',
      updatedChild,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.childrenService.remove(id);
    return this.responseService.successResponse('Child Removed Successfully');
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.is_active) {
      where['is_active'] = filter.is_active;
    }
    if (filter.age) {
      where['age'] = filter.age;
    }
    if (filter.name) {
      where['name'] = Like(`%${filter.name}%`);
    }
    if (filter.date_of_birth) {
      where['date_of_birth'] = filter.date_of_birth;
    }
    if (filter.gender) {
      where['gender'] = filter.gender;
    }
    if (filter.age) {
      where['country'] = filter.country;
    }
    if (filter.language) {
      where['language'] = filter.language;
    }
    if (filter.acadamic_performance) {
      where['acadamic_performance'] = filter.acadamic_performance;
    }
    if (filter.hand_preference) {
      where['hand_preference'] = filter.hand_preference;
    }
    if (filter.class_id) {
      where['class_id'] = filter.class_id;
    }
    if (filter.user_id) {
      where['user_id'] = filter.user_id;
    }
    return where;
  }
}
