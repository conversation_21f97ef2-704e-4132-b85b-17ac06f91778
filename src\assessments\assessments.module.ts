import { Module } from '@nestjs/common';
import { AssessmentsService } from './assessments.service';
import { AssessmentsController } from './assessments.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Assessment } from './entities/assessment.entity';
import { AssessmentExercise } from './entities/assessment-exersice.entity';
import { AssessmentQuestions } from './entities/assessment-questions.entity';
import { AssessmentMedicalCondition } from './entities/assessment-medical-condition.entity';
import { ChildrenModule } from 'src/children/children.module';
import { Child } from 'src/children/entities/child.entity';
import { CommonModule } from 'src/common/common.module';
import { QuestionsModule } from 'src/questions/questions.module';
import { UsersModule } from 'src/users/users.module';
import { ExerciseModule } from 'src/exercise/exercise.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Assessment,
      AssessmentExercise,
      AssessmentQuestions,
      AssessmentMedicalCondition,
      QuestionsModule,
    ]),
    UsersModule,
    ChildrenModule,
    CommonModule,
    QuestionsModule,
    ExerciseModule,
  ],
  controllers: [AssessmentsController],
  providers: [AssessmentsService],
  exports: [AssessmentsService],
})
export class AssessmentsModule {}
