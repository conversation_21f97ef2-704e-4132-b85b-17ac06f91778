import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from 'typeorm';
import { Plan } from '../../plans/entities/plan.entity';
import { Child } from '../../children/entities/child.entity';
import { User } from '../../users/entities/user.entity';
import { PlanPrice } from 'src/plan-prices/entities/plan-price.entity';

export enum SubscriptionStatus {
  CREATED = 'created',
  AUTHENTICATED = 'authenticated',
  ACTIVE = 'active',
  PENDING = 'pending',
  HALTED = 'halted',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  EXPIRED = 'expired',
  PAST_DUE = 'past_due',
  UNPAID = 'unpaid',
  CANCELED = 'canceled',
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  TRIALING = 'trialing',
  PAUSED = 'paused',
}

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Plan, (plan) => plan.subscriptions, { onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'plan_id' })
  plan: Plan;

  @Column({ nullable: true })
  plan_id: string;

  @ManyToOne(() => PlanPrice, (plan) => plan.subscriptions, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'plan_id' })
  PlanPrice: Plan;

  @Column({ nullable: true })
  plan_price_id: string;

  @ManyToOne(() => Child, (child) => child.subscriptions, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'child_id' })
  child: Child;

  @Column({ nullable: true })
  child_id: string;

  @ManyToOne(() => User, (user) => user.subscriptions, { onDelete: 'RESTRICT' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ nullable: true })
  user_id: string;

  @Column({ nullable: true })
  stripe_subscription_id: string;

  @Column({ type: 'timestamp', nullable: true })
  start_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  end_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  next_due_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancelled_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancellation_effective_date: Date;

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.CREATED,
  })
  status: SubscriptionStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
