import { Module } from '@nestjs/common';
import { ExerciseService } from './exercise.service';
import { ExerciseController } from './exercise.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Exercise } from './entities/exercise.entity';
import { CommonModule } from 'src/common/common.module';
import { ExerciseMedicalCondition } from './entities/exersice-medical-condition.entity';
import { MedicalConditionModule } from 'src/medical-condition/medical-condition.module';
import { ChildrenModule } from 'src/children/children.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Exercise, ExerciseMedicalCondition]),
    MedicalConditionModule,
    CommonModule,
    ChildrenModule,
  ],
  controllers: [ExerciseController],
  providers: [ExerciseService],
  exports: [ExerciseService],
})
export class ExerciseModule {}
