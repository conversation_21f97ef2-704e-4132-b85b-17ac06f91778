import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDescriptionToPlansTable1747030597910
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'plans',
      new TableColumn({
        name: 'description',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('plans', 'description');
  }
}
