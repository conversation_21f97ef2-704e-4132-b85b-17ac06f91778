import { Module } from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportController } from './report.controller';
import { UsersModule } from 'src/users/users.module';
import { AssessmentsModule } from 'src/assessments/assessments.module';
import { TransationsModule } from 'src/transations/transations.module';
import { ImageUploadModule } from 'src/image-upload/image-upload.module';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [
    UsersModule,
    AssessmentsModule,
    UsersModule,
    TransationsModule,
    ImageUploadModule,
    CommonModule,
  ],
  providers: [ReportService],
  exports: [ReportService],
  controllers: [ReportController],
})
export class ReportModule {}
