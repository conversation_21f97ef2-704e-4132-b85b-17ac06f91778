import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey,
} from 'typeorm';

export class UpdateOptionQuestionStructure1732043054778
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'option_questions',
      new TableColumn({
        name: 'level_id',
        type: 'varchar',
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      'option_questions',
      new TableForeignKey({
        columnNames: ['level_id'],
        referencedTableName: 'option_questions_levels',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );

    const table = await queryRunner.getTable('option_questions_levels');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('option_question_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('option_questions_levels', foreignKey);
    }

    await queryRunner.dropColumn(
      'option_questions_levels',
      'option_question_id',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('option_questions');
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('level_id') !== -1,
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('option_questions', foreignKey);
    }

    await queryRunner.dropColumn('option_questions', 'level_id');
    await queryRunner.addColumn(
      'option_questions_levels',
      new TableColumn({
        name: 'option_question_id',
        type: 'varchar',
        isNullable: false,
      }),
    );

    // await queryRunner.createForeignKey(
    //   'practice_exercises_levels',
    //   new TableForeignKey({
    //     columnNames: ['practice_exercise_id'],
    //     referencedTableName: 'practice_exercises',
    //     referencedColumnNames: ['id'],
    //     onDelete: 'SET NULL',
    //   }),
    // );
  }
}
