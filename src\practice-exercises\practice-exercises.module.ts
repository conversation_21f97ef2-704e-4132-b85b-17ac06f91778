import { Modu<PERSON> } from '@nestjs/common';
import { PracticeExercisesService } from './practice-exercises.service';
import { PracticeExercisesController } from './practice-exercises.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PracticeExercise } from './entities/practice-exercise.entity';
import { PracticeExerciseAge } from './entities/practice-exercise-age.entity';
import { ExerciseTypesModule } from 'src/exercise-types/exercise-types.module';
import { MedicalConditionModule } from 'src/medical-condition/medical-condition.module';
import { CommonModule } from 'src/common/common.module';
import { PracticeExercisesMedicalConditions } from './entities/practice-exersise-medical-conditions.entity';
import { PracticeExerciseLevel } from './entities/practice-exercise-level.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PracticeExercise,
      PracticeExerciseAge,
      PracticeExerciseLevel,
      PracticeExercisesMedicalConditions,
    ]),
    ExerciseTypesModule,
    MedicalConditionModule,
    CommonModule,
  ],
  controllers: [PracticeExercisesController],
  providers: [PracticeExercisesService],
})
export class PracticeExercisesModule {}
