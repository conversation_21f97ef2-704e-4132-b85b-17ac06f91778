import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { SignUpDto } from './dto/sign-up.dto';
import { ResponseService } from 'src/common/services/response.service';
import { OtpService } from 'src/otp/otp.service';
import { UserOtpService } from 'src/user-otp/user-otp.service';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Role } from 'src/common/decorators/roles.decorator';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { FindManyOptions, Like } from 'typeorm';

@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly responseService: ResponseService,
    private otpService: OtpService,
    private userOtpService: UserOtpService,
    private engagespotService: EngagespotService,
  ) {}

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post('create')
  async create(@Body() createUserDto: CreateUserDto) {
    const newUser = await this.usersService.create(createUserDto);
    await this.engagespotService.createOrUpdateUser(newUser);
    await this.engagespotService.send('send_password', [newUser.email], {
      name: newUser.name,
      email: newUser.email,
      password: createUserDto.password,
    });
    return this.responseService.successResponse('New User Created', newUser);
  }

  @Post('sign-up')
  async signUp(@Body() signUpDto: SignUpDto) {
    const userEmailExist = await this.usersService.findOneByParam({
      email: signUpDto.email,
    });
    if (userEmailExist) {
      return this.responseService.errorResponse('User with this email exist');
    }
    const user = await this.usersService.create(signUpDto);
    const otpSecret = this.otpService.generateSecret();
    await this.userOtpService.createNewUserOtpSecret(user.id, otpSecret);
    const otp = this.otpService.generateOtp(otpSecret);
    await this.engagespotService.createOrUpdateUser(user);
    await this.engagespotService.send('sent_otp', [user.email], {
      otp: otp,
      name: user.name,
    });
    return this.responseService.successResponse(
      `User Created Sucessfully`, //OTP: ${otp}
      user,
    );
  }

  @Post(':id/resent-otp')
  async resentOtp(@Param('id') user_id: string) {
    const user = await this.usersService.findOneByParam({ id: user_id }, [
      'otp',
    ]);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const otpSecret = this.otpService.generateSecret();
    await this.userOtpService.createNewUserOtpSecret(user.id, otpSecret);
    const otp = this.otpService.generateOtp(otpSecret);
    await this.engagespotService.createOrUpdateUser(user);
    await this.engagespotService.send('sent_otp', [user.email], {
      otp: otp,
      name: user.name,
    });
    return this.responseService.successResponse(
      `OTP resented Sucessfully OTP:`, // ${otp}`,
      user,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll(@Query() filter) {
    let where: any = {};
    if (filter.name) {
      where = { name: Like(`%${filter.name}%`) };
    }
    if (filter.email) {
      where = { ...where, email: Like(`%${filter.email}%`) };
    }
    if (filter.user_type) {
      where = { ...where, user_type: filter.user_type };
    }
    console.log(where);
    const userList = await this.usersService.findAll(where);
    return this.responseService.successResponse(
      'User List Successfull',
      userList,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get('paginated')
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page) || 1;
    const limit = parseInt(filter.limit) || 10;
    const skip = (page - 1) * limit;

    let where: FindManyOptions['where'] = {};
    if (filter.name) {
      where = { name: Like(`%${filter.name}%`) };
    }
    if (filter.email) {
      where = { ...where, email: Like(`%${filter.email}%`) };
    }
    if (filter.user_type) {
      where = { ...where, user_type: filter.user_type };
    }
    const [users, total] = await this.usersService.findAndCountByParam(
      where,
      skip,
      limit,
    );

    return this.responseService.successResponse('User List', {
      users: users,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @UseGuards(AccessTokenGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.usersService.findOne(id);
  }

  @UseGuards(AccessTokenGuard)
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Request() req,
  ) {
    const authUser = await this.findOne(req.user.sub);
    if (authUser.user_type !== 'super_admin' && authUser.id !== id) {
      throw new UnauthorizedException(
        'You are not authorized to update this user',
      );
    }
    const user = await this.findOne(id);
    if (!user) {
      return this.responseService.errorResponse('User Not Found');
    }
    const updatedUser = await this.usersService.update(id, updateUserDto);
    return this.responseService.successResponse(
      'User Updated Sucessfully',
      updatedUser,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.usersService.remove(id);
    return this.responseService.successResponse('User Removed');
  }
}
