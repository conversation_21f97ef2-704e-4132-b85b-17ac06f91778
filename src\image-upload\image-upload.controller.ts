import {
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Body,
  ParseFilePipeBuilder,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ImageUploadService } from './image-upload.service';
import { DeleteFileDto } from './dto/delete-image.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { throwError } from 'rxjs';

@Controller('file-upload')
export class ImageUploadController {
  constructor(private readonly imageUploadService: ImageUploadService) {}

  @UseGuards(AccessTokenGuard)
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async uploadPublicFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('file is required');
    }
    return this.imageUploadService.uploadPublicFile(
      file.buffer,
      file.originalname,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Post('delete')
  @UseGuards()
  async deletePublicFile(@Body() deleteFileDto: DeleteFileDto) {
    return this.imageUploadService.deletePublicFile(deleteFileDto);
  }
}
