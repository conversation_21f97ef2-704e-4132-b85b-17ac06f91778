import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { Exercise } from './exercise.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';

@Entity('exercise_medical_condition')
export class ExerciseMedicalCondition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Exercise, (exercise) => exercise.exerciseMedicalConditions, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'exercise_id' })
  exercise: Exercise;

  @ManyToOne(
    () => MedicalCondition,
    (medicalCondition) => medicalCondition.exerciseMedicalConditions,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'medical_condition_id' })
  medicalCondition: MedicalCondition;
}
