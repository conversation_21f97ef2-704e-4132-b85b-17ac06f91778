import {
  MigrationInterface,
  QueryRunner,
  <PERSON>Column,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class AddTypeToTransactionsTable1747046282910
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'transactions',
      new TableColumn({
        name: 'type',
        type: 'enum',
        isNullable: true,
        enum: ['subscription', 'other'],
      }),
    );
    await queryRunner.addColumn(
      'transactions',
      new TableColumn({
        name: 'subscription_id',
        type: 'varchar',
        isNullable: true,
        scale: 36,
      }),
    );
    await queryRunner.createForeignKey(
      'transactions',
      new TableForeignKey({
        columnNames: ['subscription_id'],
        referencedTableName: 'subscriptions',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('transactions', 'type');
  }
}
