import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Seeder } from 'nestjs-seeder';
import { User, UserType } from 'src/users/entities/user.entity';
import { Repository } from 'typeorm';

@Injectable()
export class SuperAdminSeeder implements Seeder {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async seed(): Promise<any> {
    const superAdminRole = await this.userRepository.findOneBy({
      user_type: UserType.ADMIN,
    });

    if (!superAdminRole) {
      const superAdmin = this.userRepository.create({
        name: 'Super Admin',
        user_type: UserType.ADMIN,
        email: '<EMAIL>',
        phone_number: '1234567890',
        password: 'Password@123',
        is_email_verified: true,
        is_active: true,
      });

      return this.userRepository.save(superAdmin);
    }
  }

  async drop(): Promise<any> {}
}
