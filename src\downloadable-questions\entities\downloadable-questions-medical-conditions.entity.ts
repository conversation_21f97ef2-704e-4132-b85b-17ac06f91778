import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { DownloadableQuestion } from './downloadable-question.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';

@Entity('downloadable_questions_medical_conditions')
export class DownloadableQuestionsMedicalCondition {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'downloadable_question_id', type: 'varchar' })
  downloadable_question_id: string;

  @ManyToOne(
    () => DownloadableQuestion,
    (question) => question.medicalConditions,
    {
      nullable: false,
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn({ name: 'downloadable_question_id' })
  downloadableQuestion: DownloadableQuestion;

  @ManyToOne(() => MedicalCondition, { nullable: false, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'medical_condition_id' })
  medicalCondition: MedicalCondition;
}
