import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  Max,
  Min,
  ValidateNested,
  ValidateIf,
  IsArray,
  IsUUID,
  ArrayMinSize,
} from 'class-validator';
import { ExerciseTypeEnum } from '../entities/exercise.entity';
import { Transform, Type } from 'class-transformer';

class RulesDto {
  @IsBoolean()
  for_assessment: boolean;

  @IsArray()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Min(0, { each: true })
  age_range: number[];

  @IsInt()
  @Min(0)
  time_limit: number; // Time limit in seconds
}

export class CreateExerciseDto {
  @IsEnum(ExerciseTypeEnum)
  @IsNotEmpty()
  type: ExerciseTypeEnum;

  @IsString()
  @IsNotEmpty()
  image: string;

  @ValidateIf(
    (o) =>
      o.type === ExerciseTypeEnum.WORD_TRACING ||
      o.type === ExerciseTypeEnum.IMAGE_TRACING,
  )
  @IsString()
  @IsNotEmpty()
  reference_image: string;

  @IsString()
  @IsOptional()
  text?: string;

  @ValidateNested()
  @Type(() => RulesDto)
  @IsNotEmpty()
  rules: RulesDto;

  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsNotEmpty()
  trigger_score: number;

  @IsArray()
  @IsUUID('all', { each: true })
  @IsOptional()
  medical_condition_ids: string[];
}
