import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsArray,
  IsString,
  IsOptional,
  IsIn,
  ValidateNested,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  Validate,
} from 'class-validator';
import { Type } from 'class-transformer';

@ValidatorConstraint({ async: false })
export class IsValidPerformanceOrder implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const object = args.object as PerformancePercentages;
    const {
      very_poor_score_trigger,
      poor_score_trigger,
      below_average_score_trigger,
      average_score_trigger,
    } = object;

    return (
      average_score_trigger > below_average_score_trigger &&
      below_average_score_trigger > poor_score_trigger &&
      poor_score_trigger > very_poor_score_trigger
    );
  }

  defaultMessage(args: ValidationArguments) {
    return 'Average score trigger must be greater than below average score trigger, which must be greater than poor score trigger, which must be greater than very poor score trigger.';
  }
}

class PerformancePercentages {
  @IsNotEmpty()
  @IsNumber()
  very_poor_score_trigger: number;

  @IsNotEmpty()
  @IsNumber()
  poor_score_trigger: number;

  @IsNotEmpty()
  @IsNumber()
  below_average_score_trigger: number;

  @IsNotEmpty()
  @IsNumber()
  average_score_trigger: number;

  @Validate(IsValidPerformanceOrder)
  validatePerformanceOrder() {}
}

export class CreatePracticeExerciseDto {
  @IsString()
  @IsNotEmpty()
  exercise_type_id: string;

  @IsOptional()
  @IsNotEmpty()
  level: number;

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  @IsNotEmpty()
  image: string;

  @IsOptional()
  @IsString()
  text?: string;

  @IsOptional()
  @IsString()
  thumbnail?: string;

  @IsString()
  @IsNotEmpty()
  reference_image: string;

  @IsArray()
  @IsNotEmpty()
  ages: number[];

  @IsNumber()
  @IsNotEmpty()
  time_limit: number;

  @IsNumber()
  @IsNotEmpty()
  trigger_score: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Type(() => String)
  medical_condition_ids?: string[];

  @ValidateNested()
  @Type(() => PerformancePercentages)
  @IsNotEmpty()
  performance_percentages: PerformancePercentages; //{ category: string; percentage: number }[];
}
