import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateDownloadableQuestionsMedicalConditionsTable1731916652640
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'downloadable_questions_medical_conditions',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            isPrimary: true,
            isGenerated: true,
            isUnique: true,
            scale: 36,
            generationStrategy: 'uuid',
          },
          {
            name: 'downloadable_question_id',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'medical_condition_id',
            type: 'varchar',
            isNullable: false,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['downloadable_question_id'],
            referencedTableName: 'downloadable_questions',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['medical_condition_id'],
            referencedTableName: 'medical_condition',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('downloadable_questions_medical_conditions');
  }
}
