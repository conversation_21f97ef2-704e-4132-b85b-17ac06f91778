import {
  Injectable,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { S3 } from 'aws-sdk';

import { v4 as uuidv4 } from 'uuid';
import { DeleteFileDto } from './dto/delete-image.dto';
import { Express } from 'express';
import { ResponseService } from 'src/common/services/response.service';

@Injectable()
export class ImageUploadService {
  private readonly s3: S3;

  constructor(private readonly response: ResponseService) {
    this.s3 = new S3({
      signatureVersion: 'v4',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    });
  }

  async uploadPublicFile(dataBuffer: Buffer, filename: string) {
    try {
      const uploadResult = await this.s3
        .upload({
          Bucket: process.env.AWS_PUBLIC_BUCKET_NAME,
          Body: dataBuffer,
          Key: `${uuidv4()}-filename-${filename}`,
          //   ContentType: 'image/jpeg',
        })
        .promise();

      const result = {
        key: uploadResult.Key,
        url: uploadResult.Location,
        filename: filename,
      };
      return this.response.successResponse('File upload completed', result);
    } catch (error) {
      console.error('Error uploading file: ', error);
      throw new InternalServerErrorException('Failed to upload file');
    }
  }

  async uploadPublicFiles(files: Express.Multer.File[]) {
    const uploadPromises = files.map((file) => this.uploadFile(file));

    try {
      const result = await Promise.all(uploadPromises);
      return this.response.successResponse('Files upload completed', result);
    } catch (error) {
      console.error('Error uploading files: ', error);
      throw new InternalServerErrorException('Failed to upload files');
    }
  }

  private async uploadFile(file: Express.Multer.File) {
    try {
      const uploadResult = await this.s3
        .upload({
          Bucket: process.env.AWS_PUBLIC_BUCKET_NAME,
          Body: file.buffer,
          Key: `${uuidv4()}-${file.originalname}`,
          ContentType: file.mimetype,
        })
        .promise();

      return {
        key: uploadResult.Key,
        url: uploadResult.Location,
        filename: file.originalname,
      };
    } catch (error) {
      console.error(`Error uploading file ${file.originalname}: `, error);
      throw new InternalServerErrorException(
        `Failed to upload file ${file.originalname}`,
      );
    }
  }

  async deletePublicFile(deleteFileDto: DeleteFileDto) {
    try {
      await this.s3
        .deleteObject({
          Bucket: process.env.AWS_PUBLIC_BUCKET_NAME,
          Key: deleteFileDto.fileId,
        })
        .promise();
      return this.response.successResponse('File deleted successfully');
    } catch (error) {
      console.error('Error deleting file: ', error);
      throw new InternalServerErrorException('Failed to delete file');
    }
  }
}

function bufferToMulterFile(
  buffer: Buffer,
  originalFile: Express.Multer.File,
): Express.Multer.File {
  return {
    ...originalFile,
    buffer,
    size: buffer.length,
    filename: `${uuidv4()}-${originalFile.originalname}`,
  };
}
