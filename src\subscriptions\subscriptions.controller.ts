import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Param,
  Patch,
  UseGuards,
  Req,
} from '@nestjs/common';
import { SubscriptionsService } from './subscriptions.service';
import { ResponseService } from '../common/services/response.service';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { Request } from 'express';

@Controller('subscriptions')
export class SubscriptionsController {
  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly responseService: ResponseService,
  ) {}

  // @UseGuards(AccessTokenGuard)
  @Post()
  async create(@Body() data: CreateSubscriptionDto, @Req() req: Request) {
    const savedSubscription =
      await this.subscriptionsService.createStripeSection(data, req['uid']);
    return this.responseService.successResponse(
      'Subscription Created Successfully',
      savedSubscription,
    );
  }

  @Get()
  async findAllPaginated(@Query() filter, @Req() req: Request) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [subscriptions, total] =
      await this.subscriptionsService.findAndCountByParam(where, skip, limit, [
        'plan',
        'child',
        'user',
      ]);

    return this.responseService.successResponse('Subscriptions List', {
      data: subscriptions,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }
  @Get('active')
  async findActive(@Query() filter, @Req() req: Request) {
    if (!filter.child_id) {
      return this.responseService.errorResponse('child_id is required');
    }
    return await this.subscriptionsService.findActiveSubscriptions(
      filter.child_id,
    );
  }
  @Patch(':id/cancel')
  async cancel(
    @Param('id') id: string,
    @Body() body: { cancellationDate: Date },
  ) {
    const cancelledSubscription =
      await this.subscriptionsService.cancelSubscription(
        id,
        body.cancellationDate,
      );
    return this.responseService.successResponse(
      'Subscription Cancelled Successfully',
      cancelledSubscription,
    );
  }

  private filterToWhere(filter: any) {
    const where: any = {};
    if (filter.plan_id) {
      where['plan_id'] = filter.plan_id;
    }
    if (filter.child_id) {
      where['child_id'] = filter.child_id;
    }
    if (filter.user_id) {
      where['user_id'] = filter.user_id;
    }
    if (filter.status) {
      where['status'] = filter.status;
    }
    return where;
  }
}
