import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { UsersModule } from 'src/users/users.module';
import { CommonModule } from 'src/common/common.module';
import { AuthTokenervice } from './services/auth-token.service';
import { OtpModule } from 'src/otp/otp.module';
import { UserOtpModule } from 'src/user-otp/user-otp.module';
import { EngagespotModule } from 'src/engagespot/engagespot.module';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '60m' },
    }),
    OtpModule,
    UsersModule,
    CommonModule,
    UserOtpModule,
    EngagespotModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, AuthTokenervice],
})
export class AuthModule {}
