import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ExerciseTypesService } from './exercise-types.service';
import { CreateExerciseTypeDto } from './dto/create-exercise-type.dto';
import { UpdateExerciseTypeDto } from './dto/update-exercise-type.dto';
import { ResponseService } from 'src/common/services/response.service';
import { filter } from 'rxjs';
import { MedicalConditionService } from 'src/medical-condition/medical-condition.service';
import { In } from 'typeorm';
import { SubscriptionsService } from 'src/subscriptions/subscriptions.service';

@Controller('exercise-types')
export class ExerciseTypesController {
  constructor(
    private readonly exerciseTypesService: ExerciseTypesService,
    private readonly responseService: ResponseService,
    private readonly medicalConditionService: MedicalConditionService,
    private readonly subscriptionsService: SubscriptionsService,
  ) {}

  @Post()
  async create(@Body() createExerciseTypeDto: CreateExerciseTypeDto) {
    const newType = await this.exerciseTypesService.create(
      createExerciseTypeDto,
    );
    let medicalConditions;
    if (
      createExerciseTypeDto.medical_condition_ids &&
      createExerciseTypeDto.medical_condition_ids.length !== 0
    ) {
      medicalConditions = await this.medicalConditionService.findByParam({
        id: In(createExerciseTypeDto.medical_condition_ids),
      });
      await this.exerciseTypesService.addMedicalConditions(
        newType.id,
        medicalConditions,
      );
    }
    return this.responseService.successResponse(
      'New Exercise Type Created Sucessfully',
      { ...newType, medicalConditions: medicalConditions },
    );
  }

  @Get()
  async findAll(@Query() filter) {
    let where: any;
    if (filter.exercise) {
      where = {
        exercise: filter.exercise,
      };
    }
    if (filter.identifier) {
      where = {
        exercise: filter.identifier,
      };
    }
    const exerciseTypes = await this.exerciseTypesService.findAll(where);
    return this.responseService.successResponse(
      'Exercise Type List',
      exerciseTypes,
    );
  }
  @Get('users')
  async findAllUser(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    let where: any;
    let subscription = null;
    if (filter.exercise) {
      where = {
        exercise: filter.exercise,
      };
    }
    if (filter.identifier) {
      where = {
        exercise: filter.identifier,
      };
    }
    if (filter.child_id) {
      subscription = await this.subscriptionsService.findActiveSubscriptions(
        filter.child_id,
      );
    }
    const is_purchased = subscription?.is_active ? true : false;

    const [customGames, total] = await this.exerciseTypesService.findAllUser(
      where,
      skip,
      limit,
      [],
    );

    const data = customGames.map((exerciseType) => {
      return {
        ...exerciseType,
        is_purchased: exerciseType.is_paid == true ? is_purchased : true,
      };
    });
    return this.responseService.successResponse('Exercise type List', {
      data,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }
  @Get('identifiers')
  async exerciseIdentifiers(@Query() filter) {
    const data = await this.exerciseTypesService.exerciseIdentifiers();
    return this.responseService.successResponse(
      'Exercise Type Identifiers',
      data,
    );
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const exerciseType = await this.exerciseTypesService.findOne(id);
    return this.responseService.successResponse(
      'Exercise Type Found',
      exerciseType,
    );
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateExerciseTypeDto: UpdateExerciseTypeDto,
  ) {
    const updateddata = await this.exerciseTypesService.update(
      id,
      updateExerciseTypeDto,
    );
    if (
      updateExerciseTypeDto.medical_condition_ids &&
      updateExerciseTypeDto.medical_condition_ids.length !== 0
    ) {
      console.log('working');
      const medicalConditions = await this.medicalConditionService.findByParam({
        id: In(updateExerciseTypeDto.medical_condition_ids),
      });
      console.log('working');
      await this.exerciseTypesService.updateMedicalConditions(
        updateddata.id,
        medicalConditions,
      );
    }
    return this.responseService.successResponse(
      'Exercise Type Updated Sucessfully',
      updateddata,
    );
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.exerciseTypesService.remove(id);
    return this.responseService.successResponse('ExersiseType Deleted');
  }
}
