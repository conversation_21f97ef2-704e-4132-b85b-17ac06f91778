import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { ExerciseResultsService } from './exercise-results.service';
import { ResponseService } from '../common/services/response.service';
import { FindManyOptions } from 'typeorm';
import { CreateExerciseResultDto } from './dto/create-exercise-result.dto';

@Controller('exercise-results')
export class ExerciseResultsController {
  constructor(
    private readonly exerciseResultsService: ExerciseResultsService,
    private readonly responseService: ResponseService,
  ) {}

  @Post()
  async create(@Body() createExerciseResultDto: CreateExerciseResultDto) {
    const savedResult = await this.exerciseResultsService.create(
      createExerciseResultDto,
    );
    return this.responseService.successResponse(
      'Exercise Result Saved Successfully',
      savedResult,
    );
  }

  @Get()
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [results, total] =
      await this.exerciseResultsService.findAndCountByParam(
        where,
        skip,
        limit,
        ['child', 'exerciseType'],
      );

    return this.responseService.successResponse('Exercise Results List', {
      data: results,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }
  @Get('analytics')
  async findAnalyticsOfChild(@Query() filter) {
    if (!filter.child_id) {
      return this.responseService.errorResponse('child_id is required');
    }
    const result = await this.exerciseResultsService.findAnalyticsOfChild(
      filter.child_id,
    );
    return this.responseService.successResponse(
      'Exercise Results Analytics',
      result,
    );
  }
  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.child_id) {
      where['child_id'] = filter.child_id;
    }
    if (filter.exercise_type_id) {
      where['exercise_type_id'] = filter.exercise_type_id;
    }
    if (filter.reference_type) {
      where['reference_type'] = filter.reference_type;
    }
    if (filter.reference_id) {
      where['reference_id'] = filter.reference_id;
    }
    return where;
  }
}
