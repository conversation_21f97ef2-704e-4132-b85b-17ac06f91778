import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IsEmail,
  IsBoolean,
} from 'class-validator';
import { UserType } from '../entities/user.entity';

export class CreateUserDto {
  @IsString()
  name: string;

  @IsEnum(UserType)
  user_type: UserType;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  phone_number: string;

  @IsString()
  password: string;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}
