import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsInt,
  IsJSON,
  ValidateIf,
  IsArray,
  IsNumber,
} from 'class-validator';

export class CreateSpeedTypingDto {
  @IsString()
  @IsNotEmpty()
  exercise_type_id: string;

  @IsString()
  @IsNotEmpty()
  question: string;

  @IsArray()
  @IsNotEmpty()
  options: Record<string, any>;

  @IsString()
  @IsOptional()
  description?: string;

  @IsInt()
  @IsOptional()
  time_limit?: number;

  @IsNumber()
  @IsOptional()
  level: number;

  @IsArray()
  @IsNotEmpty()
  ages: number[];
}
