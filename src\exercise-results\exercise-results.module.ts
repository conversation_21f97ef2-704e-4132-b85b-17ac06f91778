import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExerciseResultsService } from './exercise-results.service';
import { ExerciseResultsController } from './exercise-results.controller';
import { ExerciseResult } from './entities/exercise-result.entity';
import { ResponseService } from '../common/services/response.service';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [TypeOrmModule.forFeature([ExerciseResult]), CommonModule],
  controllers: [ExerciseResultsController],
  providers: [ExerciseResultsService, ResponseService],
})
export class ExerciseResultsModule {}
