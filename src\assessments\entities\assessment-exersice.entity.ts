import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Assessment } from './assessment.entity';
import { Exercise } from 'src/exercise/entities/exercise.entity';

@Entity('assessment_exercise')
export class AssessmentExercise {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Assessment, (assessment) => assessment.assessmentExercises, {
    onDelete: 'CASCADE',
  })
  @Column({ type: 'varchar', length: 36 })
  assessment: Assessment;

  @ManyToOne(() => Exercise, (exercise) => exercise.assessmentExercises, {
    onDelete: 'CASCADE',
  })
  @Column({ type: 'varchar', length: 36 })
  exercise: Exercise;
}
