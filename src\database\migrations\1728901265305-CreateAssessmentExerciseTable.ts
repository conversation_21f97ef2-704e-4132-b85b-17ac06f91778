import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateAssessmentExerciseTable1728901265305
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'assessment_exercise',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid',
          },
          {
            name: 'assessment_id',
            type: 'varchar',
            length: '36',
          },
          {
            name: 'exercise_id',
            type: 'varchar',
            length: '36',
          },
        ],
      }),
    );

    await queryRunner.createForeignKey(
      'assessment_exercise',
      new TableForeignKey({
        columnNames: ['assessment_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'assessments',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'assessment_exercise',
      new TableForeignKey({
        columnNames: ['exercise_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'exercise',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('assessment_exercise');
    const assessmentForeignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('assessment_id') !== -1,
    );
    const exerciseForeignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('exercise_id') !== -1,
    );

    await queryRunner.dropForeignKey(
      'assessment_exercise',
      assessmentForeignKey,
    );
    await queryRunner.dropForeignKey('assessment_exercise', exerciseForeignKey);
    await queryRunner.dropTable('assessment_exercise');
  }
}
