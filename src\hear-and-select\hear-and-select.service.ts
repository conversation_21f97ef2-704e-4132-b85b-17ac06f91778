import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateHearAndSelectDto } from './dto/create-hear-and-select.dto';
import { UpdateHearAndSelectDto } from './dto/update-hear-and-select.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HearAndSelect } from './entities/hear-and-select.entity';
import { HearAndSelectAge } from './entities/hear-and-select-age.entity';
import { CreateHearAndSelectInterface } from './interfaces/create-hear-and-select.interface';

@Injectable()
export class HearAndSelectService {
  constructor(
    @InjectRepository(HearAndSelect)
    private readonly hearAndSelectRepository: Repository<HearAndSelect>,
    @InjectRepository(HearAndSelectAge)
    private readonly hearAndSelectAgeRepository: Repository<HearAndSelectAge>,
  ) {}
  async create(createHearAndSelectInterface: CreateHearAndSelectDto) {
    const { ages, level, ...data } = createHearAndSelectInterface;

    const newHearAndSelect = this.hearAndSelectRepository.create(data);
    return await this.hearAndSelectRepository.save(newHearAndSelect);
  }
  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[HearAndSelect[], number]> {
    const [questionTypes, total] =
      await this.hearAndSelectRepository.findAndCount({
        where: params,
        relations,
        skip,
        take,
      });
    return [questionTypes, total];
  }
  findAll() {
    return `This action returns all hearAndSelect`;
  }

  async findOne(id: string) {
    const hearAndSelect = await this.hearAndSelectRepository.findOne({
      where: { id },
      relations: ['exerciseType', 'ages'],
    });
    if (!hearAndSelect) {
      throw new NotFoundException(`HearAndSelect with ID ${id} not found`);
    }
    return hearAndSelect;
  }

  async update(id: string, updateHearAndSelectDto: UpdateHearAndSelectDto) {
    const hearAndSelect = await this.findOne(id);
    const { ages, level, ...data } = updateHearAndSelectDto;
    await this.hearAndSelectRepository.update(id, data);
    return hearAndSelect;
  }

  async remove(id: string) {
    //delete hear and select
    return await this.hearAndSelectRepository.delete(id);
  }

  // Update the function to match HearAndSelect context
  async addAgeToHearAndSelect(hearAndSelect: HearAndSelect, ages: number[]) {
    await this.hearAndSelectAgeRepository.delete({ hearAndSelect });
    const ageEntities = ages.map((age) => {
      const ageEntity = new HearAndSelectAge();
      ageEntity.age = age;
      ageEntity.hear_and_select_id = hearAndSelect.id;
      return ageEntity;
    });
    return await this.hearAndSelectAgeRepository.save(ageEntities);
  }
}
