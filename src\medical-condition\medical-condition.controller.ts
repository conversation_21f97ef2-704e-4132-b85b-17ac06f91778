import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  NotFoundException,
  UseGuards,
  Query,
} from '@nestjs/common';
import { MedicalConditionService } from './medical-condition.service';
import { CreateMedicalConditionDto } from './dto/create-medical-condition.dto';
import { UpdateMedicalConditionDto } from './dto/update-medical-condition.dto';
import { ResponseService } from 'src/common/services/response.service';
import { Role } from 'src/common/decorators/roles.decorator';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { FindManyOptions, Like } from 'typeorm';

@Controller('medical-condition')
export class MedicalConditionController {
  constructor(
    private readonly medicalConditionService: MedicalConditionService,
    private readonly responseService: ResponseService,
  ) {}

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post()
  async create(@Body() createMedicalConditionDto: CreateMedicalConditionDto) {
    const newMedicalCondition = await this.medicalConditionService.create(
      createMedicalConditionDto,
    );
    return this.responseService.successResponse(
      'New Medical Condition Created Sucessfully',
      newMedicalCondition,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll() {
    const listData = await this.medicalConditionService.findAll();
    return this.responseService.successResponse(
      'Medical Condion List',
      listData,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get('paginated')
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page) || 1;
    const limit = parseInt(filter.limit) || 10;
    const skip = (page - 1) * limit;

    let where: FindManyOptions['where'] = {};

    if (filter.name) {
      where = { name: Like(`%${filter.name}%`) };
    }
    const [medicalConditions, total] =
      await this.medicalConditionService.findAndCountByParam(
        where,
        skip,
        limit,
      );

    return this.responseService.successResponse('Medical Condition List', {
      medical_conditions: medicalConditions,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const medical_condition = await this.medicalConditionService.findOne(id);
    return this.responseService.successResponse(
      'Medical Condition Detail',
      medical_condition,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateMedicalConditionDto: UpdateMedicalConditionDto,
  ) {
    const updatedMedicalCondition = await this.medicalConditionService.update(
      id,
      updateMedicalConditionDto,
    );
    return this.responseService.successResponse(
      'Medical Condition Updated Sucessfully',
      updatedMedicalCondition,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    const medicalCondition = await this.findOne(id);
    if (!medicalCondition) {
      throw new NotFoundException(`Medical condition with ID ${id} not found`);
    }
    await this.medicalConditionService.remove(id);
    return this.responseService.successResponse(
      'Medical Condition Deleted Successfully',
    );
  }
}
