import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateadnaddColumnsInChildrenTable1730270382330
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'children',
      new TableColumn({
        name: 'acadamic_performance',
        type: 'enum',
        enum: ['Below_Average', 'Average', 'Good', 'Excellent'],
        isNullable: true,
      }),
    );
    await queryRunner.dropColumn('children', 'hand_preference');
    await queryRunner.addColumn(
      'children',
      new TableColumn({
        name: 'hand_preference',
        type: 'enum',
        enum: ['Right', 'Left', 'Both'],
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('children', 'acadamic_performance');
    await queryRunner.dropColumn('children', 'hand_preference');
    await queryRunner.addColumn(
      'children',
      new TableColumn({
        name: 'hand_preference',
        type: 'enum',
        enum: ['Right', 'Left'],
        isNullable: true,
      }),
    );
  }
}
