import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Plan } from '../../plans/entities/plan.entity';
import { Subscription } from 'src/subscriptions/entities/subscription.entity';
import { Transaction } from 'src/transations/entities/transation.entity';

export enum PeriodEnum {
  MONTHLY = 'monthly',
  DAILY = 'daily',
  YEARLY = 'yearly',
  WEEKLY = 'weekly',
}

@Entity('plan_prices')
export class PlanPrice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Plan, (plan) => plan.planPrices, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'plan_id' })
  plan: Plan;

  @Column({ nullable: true })
  plan_id: string;

  @Column({ nullable: true })
  name: string;

  @Column({ type: 'longtext', nullable: true })
  description: string;

  @Column({ nullable: true })
  thumbnail: string;

  @Column({ nullable: true })
  stripe_price_id: string;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0.0,
    nullable: true,
  })
  price: number;

  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0.0,
    nullable: true,
  })
  strike_through_price: number;

  @Column({ type: 'int', default: 1, nullable: true })
  interval: number;

  @Column({
    type: 'enum',
    enum: PeriodEnum,
    default: PeriodEnum.MONTHLY,
  })
  period: PeriodEnum;

  @Column({ type: 'boolean', default: true })
  is_published: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at', nullable: true })
  deletedAt?: Date;

  @OneToMany(() => Subscription, (subscription) => subscription.plan, {
    cascade: true,
  })
  subscriptions: Subscription[];

  @OneToMany(() => Transaction, (transaction) => transaction.planPrice)
  transactions: Transaction[];
}
