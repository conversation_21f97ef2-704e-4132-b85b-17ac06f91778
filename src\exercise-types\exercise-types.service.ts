import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateExerciseTypeDto } from './dto/create-exercise-type.dto';
import { UpdateExerciseTypeDto } from './dto/update-exercise-type.dto';
import { CreateExerciseTypeInterface } from './interface/create-exercise-type.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { ExerciseType } from './entities/exercise-type.entity';
import { Repository } from 'typeorm';
import { ExerciseTypeMedicalCondition } from './entities/exercise-type-medical-conditon.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';

@Injectable()
export class ExerciseTypesService {
  constructor(
    @InjectRepository(ExerciseType)
    private exerciseTypeRepository: Repository<ExerciseType>,
    @InjectRepository(ExerciseTypeMedicalCondition)
    private exerciseTypeMedicalConditionRepository: Repository<ExerciseTypeMedicalCondition>,
  ) {}

  async create(createExerciseTypeInterface: CreateExerciseTypeInterface) {
    const newType = this.exerciseTypeRepository.create(
      createExerciseTypeInterface,
    );
    return await this.exerciseTypeRepository.save(newType);
  }

  async findAll(where: any) {
    return await this.exerciseTypeRepository.find({ where });
  }

  async findAllUser(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ): Promise<[ExerciseType[], number]> {
    return await this.exerciseTypeRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
  }
  // return await this.exerciseTypeRepository.find({ where });

  async findOne(id: string): Promise<ExerciseType> {
    const exerciseType = await this.exerciseTypeRepository.findOne({
      where: { id },
      relations: ['medicalConditions'],
    });
    if (!exerciseType) {
      throw new NotFoundException(`ExerciseType with ID ${id} not found`);
    }
    return exerciseType;
  }

  async update(id: string, updateExerciseTypeDto: UpdateExerciseTypeDto) {
    delete updateExerciseTypeDto.medical_condition_ids;
    await this.exerciseTypeRepository.update(id, updateExerciseTypeDto);
    return this.findOne(id); // Return the updated entity
  }

  async remove(id: string): Promise<void> {
    const deleteResult = await this.exerciseTypeRepository.delete(id);
    if (!deleteResult.affected) {
      throw new NotFoundException(`ExerciseType with ID ${id} not found`);
    }
  }

  async findOneByParam(
    params: { [key: string]: any },
    relations?: string[],
  ): Promise<ExerciseType | undefined> {
    return this.exerciseTypeRepository.findOne({
      where: params,
      relations,
    });
  }

  async addMedicalConditions(
    exerciseTypeId: string,
    medicalConditions: MedicalCondition[],
  ): Promise<ExerciseTypeMedicalCondition[]> {
    const exerciseType = await this.exerciseTypeRepository.findOne({
      where: { id: exerciseTypeId },
    });
    if (!exerciseType) {
      throw new Error('Exercise Type not found');
    }
    const OptionQuestionMedicalConditions = medicalConditions.map(
      (medicalCondition) => {
        return this.exerciseTypeMedicalConditionRepository.create({
          exerciseType,
          medicalCondition: medicalCondition,
        });
      },
    );
    return await this.exerciseTypeMedicalConditionRepository.save(
      OptionQuestionMedicalConditions,
    );
  }
  async updateMedicalConditions(
    exerciseTypeId: string,
    medicalConditions: MedicalCondition[],
  ): Promise<ExerciseTypeMedicalCondition[]> {
    // Remove existing medical conditions
    await this.exerciseTypeMedicalConditionRepository.delete({
      exerciseType: { id: exerciseTypeId },
    });

    // Add new medcal conditons
    const optionQuestionMedicalConditions = medicalConditions.map(
      (medicalCondition) => {
        return this.exerciseTypeMedicalConditionRepository.create({
          exerciseType: { id: exerciseTypeId },
          medicalCondition,
        });
      },
    );

    return await this.exerciseTypeMedicalConditionRepository.save(
      optionQuestionMedicalConditions,
    );
  }

  async exerciseIdentifiers() {
    const data = [
      {
        identifier: 'practice_exercise',
        name: 'Practice Exercise',
        order: 1,
      },
      {
        identifier: 'option_question',
        name: 'Option Question',
        order: 2,
      },
      {
        identifier: 'downloadable_question',
        name: 'Downloadable Question',
        order: 3,
      },
      {
        identifier: 'hear_and_select',
        name: 'Hear And Select',
        order: 4,
      },
      {
        identifier: 'speed_typing',
        name: 'Speed Typing',
        order: 5,
      },
      {
        identifier: 'matching_game',
        name: 'Matching Game',
        order: 6,
      },
      {
        identifier: 'custom_game',
        name: 'Custom Game',
        order: 7,
      },
    ];

    return data;
  }
}
