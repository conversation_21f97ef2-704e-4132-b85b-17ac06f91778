import { Module } from '@nestjs/common';
import { ExerciseTypesService } from './exercise-types.service';
import { ExerciseTypesController } from './exercise-types.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExerciseType } from './entities/exercise-type.entity';
import { CommonModule } from 'src/common/common.module';
import { ExerciseTypeMedicalCondition } from './entities/exercise-type-medical-conditon.entity';
import { MedicalConditionModule } from 'src/medical-condition/medical-condition.module';
import { SubscriptionsModule } from 'src/subscriptions/subscriptions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ExerciseType, ExerciseTypeMedicalCondition]),
    CommonModule,
    MedicalConditionModule,
    SubscriptionsModule,
  ],
  controllers: [ExerciseTypesController],
  providers: [ExerciseTypesService],
  exports: [ExerciseTypesService],
})
export class ExerciseTypesModule {}
