import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddStripePlanId1746705098801 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'plans',
      new TableColumn({
        name: 'stripe_plan_id',
        type: 'varchar',
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      'plans',
      new TableColumn({
        name: 'is_published',
        type: 'boolean',
        isNullable: true,
        default: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop both columns added in the up method
    await queryRunner.dropColumn('plans', 'is_published');
    await queryRunner.dropColumn('plans', 'stripe_plan_id');
  }
}
