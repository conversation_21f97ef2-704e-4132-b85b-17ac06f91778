import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTextToHearAndSelectTable1742375394055
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'hear_and_select',
      new TableColumn({
        name: 'text',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('hear_and_select', 'text');
  }
}
