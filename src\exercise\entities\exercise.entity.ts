import { AssessmentExercise } from 'src/assessments/entities/assessment-exersice.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ExerciseMedicalCondition } from './exersice-medical-condition.entity';
import { Transform } from 'class-transformer';

export enum ExerciseTypeEnum {
  WORD_TRACING = 'word_tracing',
  IMAGE_TRACING = 'image_tracing',
  DRAWING = 'drawing',
  PUZZLE = 'puzzle',
  ROUTE_MAPPING = 'route_mapping',
  SIMILAR_FIGURES = 'similar_figures',
}

@Entity('exercise')
export class Exercise {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ExerciseTypeEnum,
  })
  type: ExerciseTypeEnum;

  @Column({ type: 'varchar', length: 255, nullable: true })
  image: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  reference_image: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  text: string;

  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  @Column('json')
  rules: any;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  trigger_score: number;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;

  @OneToMany(
    () => AssessmentExercise,
    (assessmentExercise) => assessmentExercise.exercise,
  )
  assessmentExercises: AssessmentExercise[];

  @OneToMany(
    () => ExerciseMedicalCondition,
    (exerciseMedicalCondition) => exerciseMedicalCondition.exercise,
  )
  exerciseMedicalConditions: ExerciseMedicalCondition[];
}
