import { Controller, Get, Query } from '@nestjs/common';
import { PlanPricesService } from './plan-prices.service';
import { ResponseService } from '../common/services/response.service';
import { FindManyOptions } from 'typeorm';

@Controller('plan-prices')
export class PlanPricesController {
  constructor(
    private readonly planPricesService: PlanPricesService,
    private readonly responseService: ResponseService,
  ) {}

  @Get()
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [planPrices, total] =
      await this.planPricesService.findAndCountByParam(where, skip, limit);

    return this.responseService.successResponse('Plan Prices List', {
      data: planPrices,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.plan_id) {
      where['plan_id'] = filter.plan_id;
    }
    if (filter.is_published !== undefined) {
      where['is_published'] = filter.is_published === 'true';
    }
    return where;
  }
}
