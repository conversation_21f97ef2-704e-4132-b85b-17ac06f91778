import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { MatchingGameType } from '../entities/matching-game.entity';
import { Type } from 'class-transformer';

export class CreateMatchingGameDto {
  @IsNotEmpty()
  exercise_type_id: string;

  @IsEnum(MatchingGameType)
  @IsNotEmpty()
  type: MatchingGameType;

  @IsNotEmpty()
  question: string;

  @IsOptional()
  description?: string;

  @IsInt()
  @IsOptional()
  time_limit?: number;

  @IsNumber()
  @IsOptional()
  level: number;

  @IsArray()
  @IsNotEmpty()
  ages: number[];

  @IsNotEmpty()
  @IsArray()
  @Type(() => validationDto)
  @ValidateNested({ each: true })
  items: validationDto[];
}
class validationDto {
  @IsNotEmpty()
  item_left: string;

  @IsNotEmpty()
  item_right: string;
}
