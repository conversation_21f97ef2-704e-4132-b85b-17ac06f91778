import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddThumbanailToExerciseTypeTable1747286208043
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'exercise_types',
      new TableColumn({
        name: 'thumbnail',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('exercise_types', 'thumbnail');
  }
}
