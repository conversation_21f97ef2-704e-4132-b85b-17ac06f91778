import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PracticeExercisesService } from './practice-exercises.service';
import { CreatePracticeExerciseDto } from './dto/create-practice-exercise.dto';
import { UpdatePracticeExerciseDto } from './dto/update-practice-exercise.dto';
import { ExerciseTypesService } from 'src/exercise-types/exercise-types.service';
import { MedicalConditionService } from 'src/medical-condition/medical-condition.service';
import { FindManyOptions, In } from 'typeorm';
import { ResponseService } from 'src/common/services/response.service';
import { ExerciseEnum } from 'src/exercise-types/entities/exercise-type.entity';

@Controller('practice-exercises')
export class PracticeExercisesController {
  constructor(
    private readonly practiceExercisesService: PracticeExercisesService,
    private readonly exersiceTypeService: ExerciseTypesService,
    private readonly medicalConditionService: MedicalConditionService,
    private readonly responseService: ResponseService,
  ) {}

  @Post()
  async create(@Body() createPracticeExerciseDto: CreatePracticeExerciseDto) {
    const exerciseType = await this.exersiceTypeService.findOne(
      createPracticeExerciseDto.exercise_type_id,
    );
    if (exerciseType.exercise !== ExerciseEnum.PRACTICE_EXERCISE) {
      throw new BadRequestException('Invalid Exercise Type');
    }
    let medicalConditions = exerciseType.medicalConditions;
    if (
      createPracticeExerciseDto.medical_condition_ids &&
      createPracticeExerciseDto.medical_condition_ids.length !== 0
    ) {
      medicalConditions = await this.medicalConditionService.findByParam({
        id: In(createPracticeExerciseDto.medical_condition_ids),
      });
    }
    const newPracticeExersise = await this.practiceExercisesService.create(
      {
        image: createPracticeExerciseDto.image,
        reference_image: createPracticeExerciseDto.reference_image,
        text: createPracticeExerciseDto.text,
        time_limit: createPracticeExerciseDto.time_limit,
        performance_percentage:
          createPracticeExerciseDto.performance_percentages,
        trigger_score: createPracticeExerciseDto.trigger_score,
        exerciseType: exerciseType,
        description: createPracticeExerciseDto.description,
        thumbnail: createPracticeExerciseDto.thumbnail,
      },
      createPracticeExerciseDto.ages,
      medicalConditions,
    );
    // await this.practiceExercisesService.checkAndUpdateRankList(
    //   createPracticeExerciseDto.level,
    //   exerciseType,
    //   newPracticeExersise,
    // );
    //return newPracticeExersise;
    return this.responseService.successResponse(
      'New Practice Question Created Sucessfully',
      newPracticeExersise,
    );
  }

  @Get()
  async findAll(@Query() filter) {
    const page = parseInt(filter.page) || 1; // Default to page 1
    const limit = parseInt(filter.limit) || 10; // Default to 10 items per page
    const skip = (page - 1) * limit;

    let where: FindManyOptions['where'] = {};

    if (filter.time_limit) {
      where = { time_limit: filter.time_limit };
    }

    if (filter.trigger_score) {
      where = { ...where, trigger_score: filter.trigger_score };
    }

    if (filter.ages) {
      where = { ...where, ages: { age: In(filter.ages) } };
    }

    if (filter.exercise_type_id) {
      where = { ...where, exerciseType: { id: filter.exercise_type_id } };
    }

    const [exerciseList, total] =
      await this.practiceExercisesService.findAll(filter);

    return this.responseService.successResponse('Practice Exercise List', {
      exercises: exerciseList,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get('users')
  async findforChild(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [data, total] =
      await this.practiceExercisesService.findAndCountByParam(
        where,
        skip,
        limit,
        ['exerciseType'],
      );

    return this.responseService.successResponse('Practice exercise List', {
      data: data,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const exercise = await this.practiceExercisesService.findOne(id);
    if (!exercise) {
      throw new NotFoundException('Exercise not found');
    }
    return exercise;
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updatePracticeExerciseDto: UpdatePracticeExerciseDto,
  ) {
    const exerciseType = await this.exersiceTypeService.findOne(
      updatePracticeExerciseDto.exercise_type_id,
    );
    if (exerciseType.exercise !== ExerciseEnum.PRACTICE_EXERCISE) {
      throw new BadRequestException('Invalid Exercise Type');
    }
    let medicalConditions;
    if (
      updatePracticeExerciseDto.medical_condition_ids &&
      updatePracticeExerciseDto.medical_condition_ids.length !== 0
    ) {
      medicalConditions = await this.medicalConditionService.findByParam({
        id: In(updatePracticeExerciseDto.medical_condition_ids),
      });
    }
    const existingPracticeExercise =
      await this.practiceExercisesService.findOne(id);
    if (!existingPracticeExercise) {
      throw new NotFoundException('exersise not found');
    }
    existingPracticeExercise.image = updatePracticeExerciseDto.image
      ? updatePracticeExerciseDto.image
      : existingPracticeExercise.image;
    existingPracticeExercise.reference_image =
      updatePracticeExerciseDto.reference_image
        ? updatePracticeExerciseDto.reference_image
        : existingPracticeExercise.reference_image;
    existingPracticeExercise.text = updatePracticeExerciseDto.text
      ? updatePracticeExerciseDto.text
      : existingPracticeExercise.text;
    existingPracticeExercise.time_limit = updatePracticeExerciseDto.time_limit
      ? updatePracticeExerciseDto.time_limit
      : existingPracticeExercise.time_limit;
    existingPracticeExercise.performance_percentage =
      updatePracticeExerciseDto.performance_percentages
        ? updatePracticeExerciseDto.performance_percentages
        : existingPracticeExercise.performance_percentage;
    existingPracticeExercise.trigger_score =
      updatePracticeExerciseDto.trigger_score
        ? updatePracticeExerciseDto.trigger_score
        : existingPracticeExercise.trigger_score;
    existingPracticeExercise.exerciseType = exerciseType
      ? exerciseType
      : existingPracticeExercise.exerciseType;
    existingPracticeExercise.description = updatePracticeExerciseDto.description
      ? updatePracticeExerciseDto.description
      : existingPracticeExercise.description;
    existingPracticeExercise.thumbnail = updatePracticeExerciseDto.thumbnail
      ? updatePracticeExerciseDto.thumbnail
      : existingPracticeExercise.thumbnail;
    // Update the age range entities if there is any change
    const newAges = updatePracticeExerciseDto.ages;
    if (newAges && newAges.length !== 0) {
      existingPracticeExercise.ages =
        await this.practiceExercisesService.updateAge(
          existingPracticeExercise,
          newAges,
        );
    }

    // remove level amd medical condition
    // if (medicalConditions && medicalConditions.length > 0) {
    //   await this.practiceExercisesService.updateMedicalConditions(
    //     existingPracticeExercise,
    //     medicalConditions,
    //   );
    // }
    // if (updatePracticeExerciseDto.level) {
    //   await this.practiceExercisesService.checkAndUpdateRankList(
    //     updatePracticeExerciseDto.level,
    //     exerciseType,
    //     existingPracticeExercise,
    //   );
    // }
    const updatedPracticeExercise =
      await this.practiceExercisesService.saveEntity(existingPracticeExercise);

    return this.responseService.successResponse(
      'Practice Exercise Updated Successfully',
      updatedPracticeExercise,
    );
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    const existingPracticeExercise =
      await this.practiceExercisesService.findOne(id);
    if (!existingPracticeExercise) {
      throw new NotFoundException('Practice Exercise Not Found');
    }
    await this.practiceExercisesService.remove(id);
    return this.responseService.successResponse(
      'Practice Exercise Removed Successfully',
    );
  }

  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.ages) {
      const agesArray = Array.isArray(filter.ages)
        ? filter.ages
        : filter.ages.split(',').map((age) => parseInt(age.trim(), 10));
      where['ages'] = { age: In(agesArray) };
    }
    if (filter.exercise_type_id) {
      where['practice_exersise_type_id'] = filter.exercise_type_id;
    }
    if (filter.type) {
      where['type'] = filter.type;
    }
    return where;
  }
}
