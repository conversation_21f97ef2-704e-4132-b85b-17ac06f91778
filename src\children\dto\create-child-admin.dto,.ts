import {
  IsDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  AcadamicPerformance,
  Gender,
  HandPreference,
} from '../entities/child.entity';

export class CreateChildByAdminDto {
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @IsString()
  name: string;

  @IsInt()
  age: number;

  @IsDateString()
  date_of_birth: string;

  @IsEnum(Gender)
  gender: Gender;

  @IsString()
  @IsOptional()
  country: string;

  @IsString()
  class_id: string;

  @IsString()
  @IsOptional()
  profile_image: string;

  @IsEnum(HandPreference)
  @IsOptional()
  hand_preference: string;

  @IsEnum(AcadamicPerformance)
  @IsOptional()
  acadamic_performance: string;

  @IsString()
  @IsOptional()
  language: string;
}
