import {
  <PERSON>,
  Get,
  Res,
  HttpException,
  HttpStatus,
  Param,
  Query,
  Request,
  UseGuards,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Response } from 'express';
import { ReportService } from './report.service';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { AssessmentsService } from 'src/assessments/assessments.service';
import { UsersService } from 'src/users/users.service';
import { TransationsService } from 'src/transations/transations.service';
import { ImageUploadService } from 'src/image-upload/image-upload.service';
import { ResponseService } from 'src/common/services/response.service';

@Controller('report')
export class ReportController {
  constructor(
    private readonly reportService: ReportService,
    private readonly assessmentService: AssessmentsService,
    private readonly userService: UsersService,
    private readonly transationService: TransationsService,
    private readonly imageUploadService: ImageUploadService,
    private readonly responseService: ResponseService,
  ) {}

  @UseGuards(AccessTokenGuard)
  @Get('download')
  async downloadReport(@Query() filter): Promise<any> {
    if (!filter.assessment_id) {
      throw new BadRequestException('assessment_id is required in params');
    }
    if (!filter.report_type) {
      throw new BadRequestException('report_type is required in params');
    }
    if (filter.report_type !== 'summary' && filter.report_type !== 'detailed') {
      throw new BadRequestException(
        'Invalid report_type, must be of value summary or detailed',
      );
    }
    const { reportData, assessment } =
      await this.assessmentService.getFormattedReportData(filter.assessment_id);
    await this.transationService.checkTransation(
      assessment,
      filter.report_type,
    );
    try {
      const pdfBuffer = await this.reportService.generatePDFReportBuffer(
        reportData,
        filter.report_type,
      );

      const filename = `${assessment.id}-assessment-report-${filter.report_type}.pdf`;
      const uploadResult = await this.imageUploadService.uploadPublicFile(
        pdfBuffer,
        filename,
      );

      return uploadResult;
    } catch (error) {
      console.error('Error during report generation or upload:', error);
      throw new InternalServerErrorException(
        'Failed to generate and upload the report',
        error,
      );
    }
  }

  @Get('/pdf')
  async getPDF(@Res() res: Response): Promise<void> {
    const buffer = await this.reportService.generatePDF();

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': 'attachment; filename=example.pdf',
      'Content-Length': buffer.length,
    });

    res.end(buffer);
  }
}
