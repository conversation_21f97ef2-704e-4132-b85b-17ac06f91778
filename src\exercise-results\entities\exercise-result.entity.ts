import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColum<PERSON>,
} from 'typeorm';
import { Child } from '../../children/entities/child.entity';
import { ExerciseType } from '../../exercise-types/entities/exercise-type.entity';

@Entity('exercise_results')
export class ExerciseResult {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Child, (child) => child.exerciseResults, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'child_id' })
  child: Child;

  @Column()
  child_id: string;

  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.exerciseResults,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'exercise_type_id' })
  exerciseType: ExerciseType;

  @Column()
  exercise_type_id: string;

  @Column({
    type: 'enum',
    enum: [
      'practice_exercise',
      'option_question',
      'downloadable_question',
      'hear_and_select',
      'speed_typing',
      'matching_game',
      'custom_game',
    ],
  })
  reference_type: string;

  @Column()
  reference_id: string;

  @Column({ type: 'int' })
  time_taken: number;

  @Column({
    type: 'enum',
    enum: ['success', 'fail'],
    nullable: true,
  })
  result: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  score: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
