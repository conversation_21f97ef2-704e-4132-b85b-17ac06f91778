import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { DownloadableQuestionsService } from './downloadable-questions.service';
import { CreateDownloadableQuestionDto } from './dto/create-downloadable-question.dto';
import { UpdateDownloadableQuestionDto } from './dto/update-downloadable-question.dto';
import { Role } from 'src/common/decorators/roles.decorator';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { ResponseService } from 'src/common/services/response.service';
import { MedicalConditionService } from 'src/medical-condition/medical-condition.service';
import { FindManyOptions, In, Like } from 'typeorm';
import { ExerciseTypesService } from 'src/exercise-types/exercise-types.service';

@Controller('downloadable-questions')
export class DownloadableQuestionsController {
  constructor(
    private readonly downloadableQuestionsService: DownloadableQuestionsService,
    private readonly exerciseTypeService: ExerciseTypesService,
    private readonly responseService: ResponseService,
    private readonly medicalConditionService: MedicalConditionService,
    private readonly exersiseTypeService: ExerciseTypesService,
  ) { }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post()
  async create(
    @Body() createDownloadableQuestionDto: CreateDownloadableQuestionDto,
  ) {
    const quetion_type = await this.exerciseTypeService.findOne(
      createDownloadableQuestionDto.exercise_type_id,
    );
    if (quetion_type.exercise !== 'downloadable_question') {
      throw new BadRequestException('Invalid Exersise Type');
    }
    const level = createDownloadableQuestionDto.level;
    delete createDownloadableQuestionDto.level;
    const savedDownloadQuestion =
      await this.downloadableQuestionsService.create(
        createDownloadableQuestionDto,
      );
    savedDownloadQuestion.questionAges =
      await this.downloadableQuestionsService.addAgeToDownladableQuestion(
        savedDownloadQuestion,
        createDownloadableQuestionDto.ages,
      );
    // removed medical conditions and levels from questions
    // if (
    //   createDownloadableQuestionDto.medical_condition_ids &&
    //   createDownloadableQuestionDto.medical_condition_ids.length !== 0
    // ) {
    //   const medicalConditions = await this.medicalConditionService.findByParam({
    //     id: In(createDownloadableQuestionDto.medical_condition_ids),
    //   });
    //   savedDownloadQuestion.medicalConditions =
    //     await this.downloadableQuestionsService.addMedicalConditions(
    //       savedDownloadQuestion.id,
    //       medicalConditions,
    //     );
    // }
    // savedDownloadQuestion.medicalConditions =
    //   await this.downloadableQuestionsService.addMedicalConditions(
    //     savedDownloadQuestion.id,
    //     quetion_type.medicalConditions,
    //   );
    // await this.downloadableQuestionsService.checkAndUpdateRankList(
    //   level,
    //   quetion_type,
    //   savedDownloadQuestion,
    // );
    return this.responseService.successResponse(
      'New Downloadable Question Created Successfully',
      savedDownloadQuestion,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll(@Query() filter) {
    const page = parseInt(filter.page) || 1;
    const limit = parseInt(filter.limit) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);

    const downloadable_questions =
      await this.downloadableQuestionsService.findAll(where);

    return this.responseService.successResponse(
      'Downloadable Question List',
      downloadable_questions,
    );
  }

  @Get('users')
  async findforChild(@Query() filter) {
    let where: FindManyOptions['where'] = {};
    if (filter.ages) {
      const agesArray = Array.isArray(filter.ages)
        ? filter.ages
        : filter.ages.split(',').map((age) => parseInt(age.trim(), 10));

      where = { ages: { age: In(agesArray) } };
    }
    if (filter.exercise_type_id) {
      where = {
        ...where,
        exercise_type_id: filter.exercise_type_id,
      };
    }
    if (filter.question_type_id) {
      where = {
        ...where,
        exercise_type_id: filter.question_type_id,
      };
    }
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    // const where = this.filterToWhere(filter);
    const [downloadableQuestions, total] =
      await this.downloadableQuestionsService.findAndCountByParam(
        where,
        skip,
        limit,
        ['exerciseType'],
      );

    return this.responseService.successResponse(
      'Paginated Downloadable Question List',
      {
        downloadable_questions: downloadableQuestions,
        pagination_data: {
          currentPage: page,
          totalItems: total,
          totalPages: Math.ceil(Number(total) / limit),
        },
      },
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get('paginated')
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page, 10) || 1;
    const limit = parseInt(filter.limit, 10) || 10;
    const skip = (page - 1) * limit;

    const where = this.filterToWhere(filter);
    const [downloadableQuestions, total] =
      await this.downloadableQuestionsService.findAndCountByParam(
        where,
        skip,
        limit,
        ['exerciseType', 'level'],
      );

    return this.responseService.successResponse(
      'Paginated Downloadable Question List',
      {
        downloadable_questions: downloadableQuestions,
        pagination_data: {
          currentPage: page,
          totalItems: total,
          totalPages: Math.ceil(Number(total) / limit),
        },
      },
    );
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const downloadableQuestion =
      await this.downloadableQuestionsService.findOne(id);
    return this.responseService.successResponse(
      'Downloadable Question Details',
      downloadableQuestion,
    );
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateDownloadableQuestionDto: UpdateDownloadableQuestionDto,
  ) {
    const { exercise_type_id, level, ages, medical_condition_ids, ...rest } =
      updateDownloadableQuestionDto;
    const existingDownloadQuestion =
      await this.downloadableQuestionsService.findOne(id);

    if (!existingDownloadQuestion) {
      return this.responseService.errorResponse(
        'Downloadable Question not found',
      );
    }
    const updatedDownloadQuestion = Object.assign(
      existingDownloadQuestion,
      rest,
    );

    if (exercise_type_id) {
      const exercise_type =
        await this.exersiseTypeService.findOne(exercise_type_id);
      if (!exercise_type) {
        return this.responseService.errorResponse('Invalid Question Type ID');
      }
      updatedDownloadQuestion.exerciseType = exercise_type;
    }

    const savedDownloadQuestion =
      await this.downloadableQuestionsService.update(
        existingDownloadQuestion,
        updatedDownloadQuestion,
      );
    let questionAges;
    if (ages && ages.length > 0) {
      questionAges =
        await this.downloadableQuestionsService.updateAgesForDownloadableQuestion(
          savedDownloadQuestion,
          ages,
        );
    }

    //removed medical conditions and levels from questions

    // let medicalConditions;
    // if (medical_condition_ids && medical_condition_ids.length > 0) {
    //   medicalConditions = await this.medicalConditionService.findByParam({
    //     id: In(medical_condition_ids),
    //   });

    //   if (medicalConditions && medicalConditions.length > 0) {
    //     //savedDownloadQuestion.medicalConditions =
    //     await this.downloadableQuestionsService.updateMedicalConditions(
    //       savedDownloadQuestion.id,
    //       medicalConditions,
    //     );
    //   }
    // }
    // if (level) {
    //   await this.downloadableQuestionsService.checkAndUpdateRankList(
    //     level,
    //     savedDownloadQuestion.exerciseType,
    //     savedDownloadQuestion,
    //   );
    // }
    // savedDownloadQuestion.medicalConditions = medicalConditions;
    savedDownloadQuestion.questionAges = questionAges;
    return this.responseService.successResponse(
      'Downloadable Question Updated Successfully',
      savedDownloadQuestion,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.downloadableQuestionsService.remove(id);
    return this.responseService.successResponse(
      'Downloadable Question deleted successfully',
    );
  }

  // Utility function to handle filters
  private filterToWhere(filter: any): FindManyOptions['where'] {
    const where: FindManyOptions['where'] = {};
    if (filter.question_type_id) {
      where['exercise_type_id'] = filter.question_type_id;
    }
    if (filter.description) {
      where['description '] = Like(`%${filter.description}%`);
    }
    return where;
  }
}
