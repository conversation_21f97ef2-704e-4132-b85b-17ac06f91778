import {
  IsString,
  IsOptional,
  IsEnum,
  IsInt,
  IsArray,
  IsUUID,
  IsNotEmpty,
} from 'class-validator';

export class CreateCustomGameDto {
  @IsNotEmpty()
  exercise_type_id: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  thumbnail?: string;

  @IsEnum(['stretching_game', 'tapping_game', 'puzzle_game', 'memory_game'])
  @IsOptional()
  type?: string;

  @IsInt()
  @IsOptional()
  time_limit?: number;

  @IsInt()
  @IsOptional()
  level?: number;

  @IsArray()
  @IsInt({ each: true })
  @IsOptional()
  ages?: number[];
}
