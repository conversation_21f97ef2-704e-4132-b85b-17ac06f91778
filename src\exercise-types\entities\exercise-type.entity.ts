import { DownloadableQuestion } from 'src/downloadable-questions/entities/downloadable-question.entity';
import { DownloadableQuestionsLevel } from 'src/downloadable-questions/entities/downloadable-questions-level.entity';
import { OptionQuestionLevel } from 'src/option-questions/entities/option-questions-level.entity';
import { PracticeExerciseLevel } from 'src/practice-exercises/entities/practice-exercise-level.entity';
import { PracticeExercise } from 'src/practice-exercises/entities/practice-exercise.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  JoinTable,
  ManyToMany,
} from 'typeorm';
import { ExerciseTypeMedicalCondition } from './exercise-type-medical-conditon.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { HearAndSelect } from 'src/hear-and-select/entities/hear-and-select.entity';
import { MatchingGame } from 'src/matching-game/entities/matching-game.entity';
import { CustomGame } from 'src/custom-games/entities/custom-game.entity';
import { ExerciseResult } from 'src/exercise-results/entities/exercise-result.entity';

export enum ExerciseEnum {
  PRACTICE_EXERCISE = 'practice_exercise',
  OPTION_QUESTION = 'option_question',
  DOWNLOADABLE_QUESTION = 'downloadable_question',
  HEAR_AND_SELECT = 'hear_and_select',
  SPEED_TYPING = 'speed_typing',
  MATCHING_GAME = 'matching_game',
  CUSTOM_GAME = 'custom_game',
}

@Entity('exercise_types')
export class ExerciseType {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ExerciseEnum,
  })
  exercise: ExerciseEnum;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 255 })
  identifier: string;

  @Column({ type: 'varchar', length: 255 })
  thumbnail: string;

  @Column({ type: 'boolean' })
  is_paid: boolean;

  @OneToMany(
    () => PracticeExercise,
    (practiceExercise) => practiceExercise.exerciseType,
  )
  practiceExercises: PracticeExercise[];

  @OneToMany(
    () => HearAndSelect,
    (hearAndSelects) => hearAndSelects.exerciseType,
  )
  hearAndSelects: HearAndSelect[];

  @OneToMany(() => MatchingGame, (matchingGame) => matchingGame.exerciseType)
  matchingGames: MatchingGame[];

  @OneToMany(
    () => PracticeExerciseLevel,
    (levels) => levels.practiceExerciseType,
  )
  levels: PracticeExerciseLevel[];

  @OneToMany(
    () => OptionQuestionLevel,
    (optionQuestionLevel) => optionQuestionLevel.exerciseType,
  )
  optionQuestionLevels: OptionQuestionLevel[];

  @OneToMany(
    () => DownloadableQuestion,
    (downloadableQuestion) => downloadableQuestion.exerciseType,
  )
  downloadableQuestions: DownloadableQuestion[];

  @OneToMany(
    () => DownloadableQuestionsLevel,
    (downloadableQuestionsLevel) => downloadableQuestionsLevel.exerciseType,
  )
  downloadableQuestionsLevels: DownloadableQuestionsLevel[];

  @OneToMany(
    () => ExerciseTypeMedicalCondition,
    (exerciseTypeMedicalCondition) => exerciseTypeMedicalCondition.exerciseType,
  )
  exerciseTypeMedicalConditions: ExerciseTypeMedicalCondition[];

  @ManyToMany(() => MedicalCondition)
  @JoinTable({
    name: 'exercise_type_medical_conditions',
    joinColumn: { name: 'exercise_type_id', referencedColumnName: 'id' },
    inverseJoinColumn: {
      name: 'medical_condition_id',
      referencedColumnName: 'id',
    },
  })
  medicalConditions: MedicalCondition[];

  @OneToMany(() => CustomGame, (CustomGame) => CustomGame.exerciseType)
  customGames: CustomGame[];

  @OneToMany(
    () => ExerciseResult,
    (exerciseResult) => exerciseResult.exerciseType,
    {
      cascade: true,
    },
  )
  exerciseResults: ExerciseResult[];
}
