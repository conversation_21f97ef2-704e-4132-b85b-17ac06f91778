import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { PlansService } from './plans.service';
import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';
import { ResponseService } from 'src/common/services/response.service';
import { retry } from 'rxjs';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { Role } from 'src/common/decorators/roles.decorator';
import { RolesGuard } from 'src/common/guards/roles.guard';

@Controller('plans')
export class PlansController {
  constructor(
    private readonly plansService: PlansService,
    private readonly responseService: ResponseService,
  ) {}

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post()
  async create(@Body() createPlanDto: CreatePlanDto) {
    const newPlan = await this.plansService.create(createPlanDto);
    return this.responseService.successResponse(
      'New Plan Created Sucessfully',
      newPlan,
    );
  }

  @Get()
  async findAll() {
    const PlanList = await this.plansService.findAll();
    return this.responseService.successResponse('Plan List', PlanList);
  }

  @Get('subscription')
  async findAllSubscriptions(@Query () filterDto: any) {
    const PlanList = await this.plansService.findAllSubscriptions(filterDto);
    return this.responseService.successResponse('Plan List', PlanList);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const plan = await this.plansService.findOne(id);
    return this.responseService.successResponse('plan fetch sucessfull', plan);
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updatePlanDto: UpdatePlanDto) {
    const plan = await this.plansService.update(id, updatePlanDto);
    return this.responseService.successResponse(
      'Plan Updated Sucessfully',
      plan,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.plansService.remove(id);
    return this.responseService.successResponse('Plan Removed Sucessfully');
  }
}
