import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';
import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  ManyToOne,
  JoinColumn,
  ManyToMany,
  JoinTable,
  OneToOne,
} from 'typeorm';
import { OptionQuestionsMedicalConditions } from './option-question-medical-condition.entity';
import { OptionQuestionsAge } from './option-questions-age.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { OptionQuestionLevel } from './option-questions-level.entity';

@Entity('option_questions') // Define the table name
export class OptionQuestion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ExerciseType) // Many-to-one relation with ExerciseType
  @JoinColumn({ name: 'exercise_type_id' })
  exerciseType: ExerciseType;

  @Column({ type: 'varchar' })
  exercise_type_id: string;

  @Column({ type: 'varchar' })
  question_image: string;

  @Column({ type: 'varchar' })
  option_1_image: string;

  @Column({ type: 'varchar' })
  option_2_image: string;

  @Column({ type: 'varchar' })
  option_3_image: string;

  @Column({ type: 'varchar' })
  option_4_image: string;

  @Column({ type: 'varchar', nullable: true })
  option_5_image: string;

  @Column({ type: 'json' })
  correct_options: any;

  @Column({ type: 'int' })
  time_limit: number;

  @Column({ type: 'text', nullable: true })
  answer_description: string;

  @Column({ nullable: true })
  thumbnail: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;

  @ManyToMany(() => MedicalCondition)
  @JoinTable({
    name: 'option_questions_medical_conditions',
    joinColumn: { name: 'option_question_id', referencedColumnName: 'id' },
    inverseJoinColumn: {
      name: 'medical_condition_id',
      referencedColumnName: 'id',
    },
  })
  medicalConditions: MedicalCondition[];

  @Column({ nullable: true })
  text: string;

  @OneToMany(
    () => OptionQuestionsMedicalConditions,
    (medicalCondition) => medicalCondition.optionQuestion,
  )
  medical_conditions: OptionQuestionsMedicalConditions[];

  @OneToMany(() => OptionQuestionsAge, (age) => age.optionQuestion)
  ages: OptionQuestionsAge[];

  @OneToOne(() => OptionQuestionLevel, { nullable: true })
  @JoinColumn({ name: 'level_id' })
  level: OptionQuestionLevel;

  @Column({ nullable: true })
  description?: string; // Added description column
}
