import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { SpeedTyping } from './speed-typing.entity';

@Entity('speed_typing_age')
export class SpeedTypingAge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => SpeedTyping, (speedTyping) => speedTyping.ages, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'speed_typing_id' }) // Explicitly specify the column name
  speedTyping: SpeedTyping;

  @Column()
  age: number;

  @Column()
  speed_typing_id: string;
}
