import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum OptionTypeEnum {
  PRIMARY = 'primary',
  SUB = 'sub',
}

export enum QuestionOptionTypeEnum {
  MEDICAL = 'medical',
  QUESTIONARE = 'questionare',
}

@Entity('question_options')
export class QuestionOption {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  option: string;

  @Column({
    type: 'enum',
    enum: OptionTypeEnum,
  })
  type: OptionTypeEnum;

  @Column({
    type: 'enum',
    enum: QuestionOptionTypeEnum,
  })
  question_type: QuestionOptionTypeEnum;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updated_at: Date;
}
