import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  JoinColumn,
  OneToOne,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { DownloadableQuestionsAge } from './downloadable-questions-age.entity';
import { DownloadableQuestionsMedicalCondition } from './downloadable-questions-medical-conditions.entity';
import { DownloadableQuestionsLevel } from './downloadable-questions-level.entity';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { ExerciseType } from 'src/exercise-types/entities/exercise-type.entity';

@Entity('downloadable_questions')
export class DownloadableQuestion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  question_file: string;

  // @Column({ type: 'varchar' })
  // question_type_id: string;

  @Column({ type: 'longtext', nullable: true })
  description: string;

  @Column({ nullable: true })
  text: string;

  @Column({ type: 'varchar' })
  level_id: string;

  @Column({ type: 'varchar', nullable: true })
  exercise_type_id: string;

  @Column({ nullable: true })
  thumbnail: string;

  @ManyToOne(
    () => ExerciseType,
    (exerciseType) => exerciseType.downloadableQuestions,
    {
      onDelete: 'SET NULL',
    },
  )
  @JoinColumn({ name: 'exercise_type_id' })
  exerciseType: ExerciseType;

  // @ManyToOne(() => DownloadableQuestionType, (type) => type.questions, {
  //   nullable: false,
  //   onDelete: 'CASCADE',
  // })
  // @JoinColumn({ name: 'question_type_id' })
  // questionType: DownloadableQuestionType;

  @OneToMany(() => DownloadableQuestionsAge, (age) => age.downloadableQuestion)
  questionAges: DownloadableQuestionsAge[];

  @OneToMany(
    () => DownloadableQuestionsMedicalCondition,
    (condition) => condition.downloadableQuestion,
  )
  medical_conditions: DownloadableQuestionsMedicalCondition[];

  @ManyToMany(() => MedicalCondition)
  @JoinTable({
    name: 'downloadable_questions_medical_conditions',
    joinColumn: {
      name: 'downloadable_question_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'medical_condition_id',
      referencedColumnName: 'id',
    },
  })
  medicalConditions: MedicalCondition[];

  @OneToOne(() => DownloadableQuestionsLevel, { nullable: true })
  @JoinColumn({ name: 'level_id' })
  level: DownloadableQuestionsLevel;
}
