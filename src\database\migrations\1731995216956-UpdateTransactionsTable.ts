import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateTransactionsTable1731995216956
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add session_id column
    await queryRunner.addColumn(
      'transactions',
      new TableColumn({
        name: 'session_id',
        type: 'varchar',
        isNullable: true,
      }),
    );

    // Add note column
    await queryRunner.addColumn(
      'transactions',
      new TableColumn({
        name: 'note',
        type: 'varchar',
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove note column
    await queryRunner.dropColumn('transactions', 'note');

    // Remove session_id column
    await queryRunner.dropColumn('transactions', 'session_id');
  }
}
