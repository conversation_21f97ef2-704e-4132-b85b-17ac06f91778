import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  NotFoundException,
  ConflictException,
  UseGuards,
  Request,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { AssessmentsService } from './assessments.service';
import { CreateAssessmentDto } from './dto/create-assessment.dto';
import {
  UpdateAssessmentDto,
  UploadAssessmentExerciseScoreDto,
} from './dto/update-assessment.dto';
import { ChildrenService } from 'src/children/children.service';
import { ResponseService } from 'src/common/services/response.service';
import { UploadAssessmentAnswersDto } from './dto/assessment-answers.dto';
import { AssessmentStatus } from './entities/assessment.entity';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { UsersService } from 'src/users/users.service';
import { UserType } from 'src/users/entities/user.entity';
import { In } from 'typeorm';
import { QuestionsService } from 'src/questions/questions.service';
import { ExerciseService } from 'src/exercise/exercise.service';
import { MedicalCondition } from 'src/medical-condition/entities/medical-condition.entity';
import { Exercise } from 'src/exercise/entities/exercise.entity';
import { AssessmentMedicalCondition } from './entities/assessment-medical-condition.entity';

@Controller('assessments')
export class AssessmentsController {
  constructor(
    private readonly assessmentsService: AssessmentsService,
    private readonly childrenService: ChildrenService,
    private readonly responseService: ResponseService,
    private readonly userService: UsersService,
    private readonly questionService: QuestionsService,
    private readonly exersiseService: ExerciseService,
  ) {}

  @UseGuards(AccessTokenGuard)
  @Post('/child/:id/start')
  async create(@Param('id') child_id: string, @Request() req) {
    const child = await this.childrenService.findOne(child_id);

    if (!child) {
      throw new NotFoundException('Child Not Found');
    }
    const data = await this.assessmentsService.startAssessment(child);
    return this.responseService.successResponse('assessment start', data);
  }

  @UseGuards(AccessTokenGuard)
  @Post('/child/:id/questionare')
  async continueAssessment(@Param('id') child_id: string, @Request() req) {
    const child = await this.childrenService.findOne(child_id);

    if (!child) {
      throw new NotFoundException('Child Not Found');
    }
    const data = await this.assessmentsService.continueAssessment(child);
    return this.responseService.successResponse('assessment continue', data);
  }

  @UseGuards(AccessTokenGuard)
  @Post(':id/answers')
  async assessmentAnswers(
    @Param('id') id: string,
    @Body() assessmentAnswersDto: UploadAssessmentAnswersDto,
  ) {
    const assessment = await this.assessmentsService.findOneByParam({ id }, [
      'assessmentQuestions',
      'assessmentQuestions.question',
    ]);
    if (!assessment) {
      throw new NotFoundException('Assessment Not Found');
    }
    if (assessment.status === AssessmentStatus.COMPLETED) {
      throw new ConflictException('This Assessment is already Completed');
    }
    if (assessment.questionare_pass == true) {
      throw new ConflictException(
        'This Assessments Questionare is already Completed',
      );
    }
    const data = await this.assessmentsService.uploadAssessmentAnswers(
      assessment,
      assessmentAnswersDto.answers,
    );
    return this.responseService.successResponse('answers uploaded', data);
  }

  @UseGuards(AccessTokenGuard)
  @Post(':id/exercise/:exerciseType/upload-score')
  async uploadAssessmentExersiseScore(
    @Param('id') id: string,
    @Param('exerciseType') exerciseType: string,
    @Body() scoreDto: UploadAssessmentExerciseScoreDto,
  ) {
    if (exerciseType !== 'word-tracing' && exerciseType !== 'image-tracing') {
      throw new BadRequestException('Invalid Api Route');
    }
    const assessment = await this.assessmentsService.findOne(id);
    const exercise = await this.exersiseService.findOne(scoreDto.exercise_id);
    if (!assessment) {
      throw new NotFoundException('Assessment Not Found');
    }
    if (assessment.questionare_pass !== true) {
      throw new ConflictException('Assessment questionare is not compeleted');
    }
    if (assessment.status === AssessmentStatus.COMPLETED) {
      throw new ConflictException('This Assessment is already Completed');
    }
    if (!exercise) {
      throw new NotFoundException('Exercise Not Found');
    }
    if (
      exercise.rules.for_assessment &&
      exercise.rules.for_assessment == false
    ) {
      throw new ConflictException('This Exercise is not for Assessment');
    }
    if (exercise.trigger_score > scoreDto.score) {
      let assessmentMedicalConditions = assessment.assessmentMedicalConditions;
      for (const exerciseMedicalConditon of exercise.exerciseMedicalConditions) {
        const newAssessmentMedicalCondition = new AssessmentMedicalCondition();
        newAssessmentMedicalCondition.assessment_id = assessment.id;
        newAssessmentMedicalCondition.medicalCondition =
          exerciseMedicalConditon.medicalCondition;
        assessmentMedicalConditions.push(newAssessmentMedicalCondition);
      }
      await this.assessmentsService.saveAssessmentMedicalConditons(
        assessmentMedicalConditions,
      );
    }
    if (exerciseType == 'word-tracing') {
      assessment.word_tracing_exersice_score = scoreDto.score;
    }
    if (exerciseType == 'image-tracing') {
      assessment.image_tracing_exersice_score = scoreDto.score;
    }

    if (
      assessment.word_tracing_exersice_score !== null &&
      assessment.image_tracing_exersice_score !== null &&
      assessment.medical_questions_pass == true &&
      assessment.questionare_pass == true
    ) {
      assessment.status = AssessmentStatus.COMPLETED;
    }
    const data = await this.assessmentsService.saveInstance(assessment);
    return this.responseService.successResponse(
      `${exerciseType} score uploaded sucessfully`,
      data,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Post(':id/medical-question/complete')
  async makeBasicQuestionPass(@Param('id') id: string) {
    const assessment = await this.assessmentsService.findOneByParam({ id }, [
      'assessmentQuestions.question',
    ]);
    if (!assessment) {
      throw new NotFoundException('Assessment Not Found');
    }
    const passBool =
      await this.assessmentsService.checkAllMedicalQuestionAnswered(
        assessment.assessmentQuestions,
      );
    if (passBool == false) {
      throw new BadRequestException(
        'not all question is answered cannot continue',
      );
    }
    assessment.medical_questions_pass = true;
    await this.assessmentsService.saveInstance(assessment);
    return this.responseService.successResponse(
      'Successfully changed status of medical question',
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  async findAll(@Request() req, @Query() filter) {
    const user = await this.userService.findOneByParam({ id: req.user.sub }, [
      'children',
    ]);
    const assessments = await this.assessmentsService.findAll(filter, user);
    return this.responseService.successResponse('assessment list', assessments);
  }

  @UseGuards(AccessTokenGuard)
  @Get('/child/:childId/check-status')
  async checkAssesmentStatus(@Param('childId') child_id: string) {
    const statusData =
      await this.assessmentsService.checkAssessmentStatus(child_id);
    return this.responseService.successResponse(
      'Assessment Status Fetched Sucessfully',
      statusData,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.assessmentsService.findOne(id);
  }

  @UseGuards(AccessTokenGuard)
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAssessmentDto: UpdateAssessmentDto,
  ) {
    return this.assessmentsService.update(+id, updateAssessmentDto);
  }

  @UseGuards(AccessTokenGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.assessmentsService.remove(+id);
  }
}
