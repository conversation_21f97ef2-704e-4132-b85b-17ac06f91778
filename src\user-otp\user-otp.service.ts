import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserOtp } from './entities/user-otp.entity';
import { Repository } from 'typeorm';
import { UsersService } from 'src/users/users.service';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class UserOtpService {
  constructor(
    @InjectRepository(UserOtp)
    private userOtpRepository: Repository<UserOtp>,
    private readonly userService: UsersService,
  ) {}

  async createNewUserOtpSecret(user_id: string, otp_secret: string) {
    const userOtp = await this.userOtpRepository.findOne({
      where: { user_id },
    });

    if (userOtp) {
      await this.deleteUserOtp(userOtp);
    }

    const newOtp = await this.userOtpRepository.create({
      user_id,
      otp_secret,
      otp_timestamp: new Date(),
    });
    return await this.userOtpRepository.save(newOtp);
  }

  async findOneByUserId(userId: string): Promise<UserOtp> {
    return await this.userOtpRepository.findOne({
      where: { user_id: userId },
    });
  }

  async deleteUserOtpByUserId(user_id: string) {
    await this.userOtpRepository.delete({ user_id });
    return true;
  }

  async deleteUserOtp(userOtp: UserOtp) {
    await this.userOtpRepository.delete(userOtp);
    return true;
  }
}
