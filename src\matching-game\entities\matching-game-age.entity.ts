import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { MatchingGame } from './matching-game.entity';

@Entity('matching_game_age')
export class MatchingGameAge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => MatchingGame, (matchingGame) => matchingGame.ages, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'matching_game_id' }) // Explicitly specify the column name
  matchingGame: MatchingGame;

  @Column()
  age: number;

  @Column()
  matching_game_id: string;
}
