import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'typeorm';
import { OptionQuestion } from './option-question.entity';

@Entity('option_questions_age')
export class OptionQuestionsAge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar')
  option_questions_id: string;

  @ManyToOne(() => OptionQuestion, (optionQuestions) => optionQuestions.ages)
  @JoinColumn({ name: 'option_questions_id' })
  optionQuestion: OptionQuestion;

  @Column({ type: 'int' })
  age: number;
}
