import {
  IsEmail,
  <PERSON>NotEmpt<PERSON>,
  Is<PERSON><PERSON>al,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class BillingAddressChild {
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsString()
  phone_number: string;

  @IsNotEmpty()
  @IsString()
  city: string;

  @IsNotEmpty()
  @IsString()
  state: string;

  @IsNotEmpty()
  @IsString()
  postal_code: string;

  @IsNotEmpty()
  @IsString()
  line1: string;

  @IsNotEmpty()
  @IsString()
  line2: string;
}

export class CreateSubscriptionDto {
  @IsOptional()
  success_url?: string;

  @IsOptional()
  cancel_url?: string;

  @IsNotEmpty()
  @IsString()
  child_id: string;

  @IsOptional()
  @IsString()
  plan_id: string;

  @IsNotEmpty()
  @IsString()
  plan_price_id: string;

  @IsOptional()
  total_count: number;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => BillingAddressChild)
  billing_address: BillingAddressChild;
}
