import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MatchingGame } from './entities/matching-game.entity';
import { MatchingGameAge } from './entities/matching-game-age.entity';
import { MatchingGameItems } from './entities/matching-game-items.entity';
import { MatchingGameService } from './matching-game.service';
import { MatchingGameController } from './matching-game.controller';
import { ExerciseTypesModule } from 'src/exercise-types/exercise-types.module';
import { CommonModule } from 'src/common/common.module';
import { UsersModule } from 'src/users/users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      MatchingGame,
      MatchingGameAge,
      MatchingGameItems,
    ]),
    CommonModule,
    UsersModule,
    ExerciseTypesModule,
  ],
  controllers: [MatchingGameController],
  providers: [MatchingGameService],
})
export class MatchingGameModule {}
