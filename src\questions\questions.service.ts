import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { CreateQuestionInterface } from './interface/create-questions.interface';
import { InjectRepository } from '@nestjs/typeorm';
import { Question } from './entities/question.entity';
import { In, Repository } from 'typeorm';
import { QuestionMedicalCondition } from './entities/question-medical-condition.entity';
import { MedicalConditionService } from 'src/medical-condition/medical-condition.service';
import { UpdateQuestionInterface } from './interface/update-question.inteface';

@Injectable()
export class QuestionsService {
  constructor(
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(QuestionMedicalCondition)
    private questionMedicalConditionRepository: Repository<QuestionMedicalCondition>,
    private readonly medicalConditionService: MedicalConditionService,
  ) {}
  async create(createQuestionInterface: CreateQuestionInterface) {
    const newQuestion = this.questionRepository.create(createQuestionInterface);
    return await this.questionRepository.save(newQuestion);
  }

  async addMedicalConditionsToQuestion(
    question: Question,
    medicalConditionIds: string[],
  ): Promise<Question> {
    if (
      question.questionMedicalConditions &&
      question.questionMedicalConditions.length > 0
    ) {
      question.questionMedicalConditions.map(
        async (questionMedicalCondition) => {
          await this.questionMedicalConditionRepository.delete(
            questionMedicalCondition.id,
          );
        },
      );
    }
    for (const conditionId of medicalConditionIds) {
      const medicalCondition =
        await this.medicalConditionService.findOne(conditionId);
      if (!medicalCondition) {
        throw new NotFoundException(
          `Medical condition with ID ${conditionId} not found`,
        );
      }
      const questionMedicalCondition =
        this.questionMedicalConditionRepository.create({
          question,
          medical_condition: medicalCondition,
        });
      await this.questionMedicalConditionRepository.save(
        questionMedicalCondition,
      );
    }
    return this.questionRepository.findOne({
      where: { id: question.id },
      relations: [
        'questionMedicalConditions',
        'questionMedicalConditions.medical_condition',
      ],
    });
  }

  async findAll() {
    return await this.questionRepository.find();
  }

  async findAllMedicalQuestions() {
    const allQuestions = await this.questionRepository.find();
    const medicalQuestions = allQuestions.filter(
      (question) => question.rules.medical_question === true,
    );
    return medicalQuestions;
  }

  async findAndCountByParam(
    params: { [key: string]: any },
    skip: number,
    take: number,
    relations?: string[],
  ) {
    const [Questions, total] = await this.questionRepository.findAndCount({
      where: params,
      relations,
      skip,
      take,
    });
    return [Questions, total];
  }

  async findByIds(questionIds: string[]): Promise<Question[]> {
    return this.questionRepository.findBy({ id: In(questionIds) });
  }

  async findOne(id: string) {
    return await this.questionRepository.findOne({
      where: { id },
      relations: ['questionMedicalConditions.medical_condition'],
    });
  }

  async findByParam(params: { [key: string]: any }, relations?: string[]) {
    // Find and return the record based on dynamic parameters
    // return this.userRepository.findOneBy(params);
    return this.questionRepository.find({
      where: params,
      relations,
    });
  }

  async update(id: string, updateQuestionDto: UpdateQuestionInterface) {
    const question = await this.questionRepository.findOne({
      where: { id },
      relations: ['questionMedicalConditions'],
    });

    if (!question) {
      throw new NotFoundException(`Question with ID #${id} not found`);
    }
    // const updateFields = Object.keys(UpdateQuestionDto).reduce(
    //   (fields, key) => {
    //     if (UpdateQuestionDto[key] !== undefined) {
    //       fields[key] = UpdateQuestionDto[key];
    //     }
    //     return fields;
    //   },
    //   {} as Partial<UpdateQuestionDto>,
    // );
    // console.log(updateQuestionDto);
    Object.assign(question, updateQuestionDto);
    return await this.questionRepository.save(question);
  }

  async remove(id: string) {
    const question = await this.questionRepository.findOne({ where: { id } });
    if (!question) {
      throw new NotFoundException('question Not found');
    }
    return await this.questionRepository.delete(id);
  }

  async save(question: Question) {
    return await this.questionRepository.save(question);
  }
}
