import {
  ConflictException,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Assessment } from 'src/assessments/entities/assessment.entity';
import { Plan } from 'src/plans/entities/plan.entity';
import {
  TransactionStatus,
  TransactionType,
} from 'src/transations/entities/transation.entity';
import { TransationsService } from 'src/transations/transations.service';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import Stripe from 'stripe';
import { StripeSubscriptionService } from './services/stripe-subscription.service';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(
    private readonly usersService: UsersService,
    private transactionService: TransationsService,
    private stripeSubscriptionService: StripeSubscriptionService,
  ) {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
  }

  async createCheckoutSession(
    client_id: string,
    plan: Plan,
    assessment: Assessment,
  ) {
    // Ensure necessary services are available
    const stripe = await this.initiateInstance();

    // Check for existing transactions
    const existingTransaction = await this.transactionService.findOneByParam({
      user_id: client_id,
      plan: plan,
      assessment: assessment,
    });

    if (existingTransaction) {
      switch (existingTransaction.status) {
        case TransactionStatus.COMPLETED:
          throw new ConflictException('Payment already completed');

        case TransactionStatus.PENDING:
          // Check if session is expired
          const isSessionExpired = await this.isStripeSessionExpired(
            existingTransaction.session_id,
          );

          if (!isSessionExpired) {
            return {
              sessionId: existingTransaction.session_id,
              url: await this.getStripeSessionUrl(
                existingTransaction.session_id,
              ),
            };
          }

          // If session expired, create a new one
          return await this.createNewSession(client_id, plan, assessment);

        case TransactionStatus.FAILED:
          // Create a new session for failed transactions
          return await this.createNewSession(client_id, plan, assessment);

        default:
          throw new ConflictException('Unexpected transaction status');
      }
    }

    // If no existing transaction, create a new session
    return await this.createNewSession(client_id, plan, assessment);
  }

  /**
   * Check if a Stripe session is expired
   */
  private async isStripeSessionExpired(sessionId: string): Promise<boolean> {
    const stripe = await this.initiateInstance();
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // Assuming a session is expired if its status is 'expired'
    return session.status === 'expired';
  }

  // Retrieve the Stripe session URL
  private async getStripeSessionUrl(sessionId: string): Promise<string> {
    const stripe = await this.initiateInstance();
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    return session.url;
  }

  //Create a new Stripe checkout session and transaction

  private async createNewSession(
    client_id: string,
    plan: Plan,
    assessment: Assessment,
  ) {
    const client = await this.usersService.findOne(client_id);
    if (!plan) {
      throw new ConflictException('Plan not present');
    }

    const stripe_customer =
      await this.getStripeCustomerIdOrCreateCustomer(client);
    if (!stripe_customer) {
      throw new ConflictException('Could not create Stripe customer');
    }

    try {
      const stripe = await this.initiateInstance();

      const session = await stripe.checkout.sessions.create({
        line_items: [
          {
            price_data: {
              currency: 'gbp',
              product_data: {
                name: plan.name,
              },
              unit_amount: await this.convertAmountToPence(
                plan.discount_amount,
              ),
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        customer: stripe_customer,
        success_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        cancel_url: 'https://www.youtube.com/watch?v=b3rNUhDqciM',
        metadata: {
          client_id: client.id,
          assessment_id: assessment.id,
          plan_id: plan.id,
        },
      });

      const transaction =
        await this.transactionService.getStripeTransactionInstance();
      transaction.user_id = client.id;
      transaction.assessment_id = assessment.id;
      transaction.amount = plan.discount_amount;
      transaction.plan_id = plan.id;
      transaction.session_id = session.id;
      transaction.status = TransactionStatus.PENDING;
      await this.transactionService.updateEntity(transaction);

      return {
        sessionId: session.id,
        url: session.url,
      };
    } catch (error) {
      console.error('Stripe Checkout Session Error:', error.message);
      throw new ConflictException('Something went wrong, please try again');
    }
  }

  // Verify the payment session
  async verifySession(sessionId: string) {
    return await this.stripe.checkout.sessions.retrieve(sessionId);
  }

  async initiateInstance() {
    const Stripe = require('stripe');
    return this.stripe;
  }

  async createCustomer(client: User) {
    const email = client.email;
    const name = client.name;
    const phone = client.phone_number;

    const stripe = await this.initiateInstance();
    try {
      const customer = await stripe.customers.create({
        email,
        name,
        phone,
      });
      client.stripe_customer_id = customer.id;
      await this.usersService.updateEntity(client);
      return customer.id;
    } catch (ex) {
      console.log(ex.message, 'create user stripe');
      return false;
    }
  }

  async getStripeCustomerIdOrCreateCustomer(client: User) {
    let customer;
    if (client.stripe_customer_id) {
      customer = client.stripe_customer_id;
    } else {
      customer = await this.createCustomer(client);
    }
    return customer;
  }

  async createPaymentIntent(
    client_id: string,
    plan: Plan,
    assessment: Assessment,
  ) {
    const stripe = await this.initiateInstance();
    const client = await this.usersService.findOne(client_id);
    if (plan == null) {
      throw new ConflictException({
        status: false,
        message: 'Plan not present',
      });
    }
    const stripe_customer =
      await this.getStripeCustomerIdOrCreateCustomer(client);
    if (!stripe_customer) {
      return false;
    }
    console.log(
      plan.discount_amount,
      this.convertAmountToPence(plan.discount_amount),
    );
    const paymentIntent = await stripe.paymentIntents.create({
      amount: await this.convertAmountToPence(plan.discount_amount),
      currency: 'gbp',
      automatic_payment_methods: { enabled: true },
      customer: stripe_customer,
      setup_future_usage: 'off_session',
    });

    const transaction =
      await this.transactionService.getStripeTransactionInstance();
    transaction.user_id = client.id;
    transaction.assessment_id = assessment.id;
    transaction.amount = plan.discount_amount;
    transaction.plan_id = plan.id;
    transaction.payment_indent = paymentIntent.id;
    await this.transactionService.updateEntity(transaction);
    if (!paymentIntent) {
      throw new ConflictException({
        status: false,
        message: 'something went wrong please try again',
      });
    }
    const result = {
      payment_intent: paymentIntent.id,
      client_secret: paymentIntent.client_secret,
    };

    return result;
  }

  async convertAmountToPence(amount: number) {
    return amount * 100;
  }

  constructEvent(payload: Buffer, signature: string, webhookSecret: string) {
    return this.stripe.webhooks.constructEvent(
      payload,
      signature,
      webhookSecret,
    );
  }

  async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    const paymentIntentId = paymentIntent.id;
    const transaction = await this.transactionService.findOneByParam({
      payment_indent: paymentIntent,
    });
    if (!transaction) {
      console.log('no transation with paytment indent found');
    }
    transaction.status = TransactionStatus.COMPLETED;
    await this.transactionService.updateEntity;
    console.log(`Payment succeeded for payment intent ID: ${paymentIntentId}`);
    // Update your order or transaction record in the database to reflect the successful payment
  }

  async handleCheckoutSessionCompleted(session: any) {
    const { client_id, assessment_id, plan_id } = session.metadata;

    // Mark the transaction as completed
    const transaction = await this.transactionService.findOneByParam({
      session_id: session.id,
    });

    if (transaction) {
      if (transaction.type == TransactionType.SUBSCRIPTION) {
        await this.stripeSubscriptionService.createSubscription(
          transaction,
          session,
        );
      } else {
        transaction.status = TransactionStatus.COMPLETED;
        await this.transactionService.updateEntity(transaction);
      }

      console.log('Transaction marked as completed:', transaction);
    }

    // Additional logic (e.g., notifying the user, updating related entities)
  }

  async handlePaymentFailure(paymentIntent: any) {
    const transaction = await this.transactionService.findOneByParam({
      payment_indent: paymentIntent.id,
    });

    if (transaction) {
      transaction.status = TransactionStatus.FAILED;
      await this.transactionService.updateEntity(transaction);
      console.log('PaymentIntent marked as failed:', transaction);
    }
  }

  // subscription
}
