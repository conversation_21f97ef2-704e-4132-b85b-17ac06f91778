import { <PERSON>du<PERSON> } from '@nestjs/common';
import { QuestionsService } from './questions.service';
import { QuestionsController } from './questions.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Question } from './entities/question.entity';
import { CommonModule } from 'src/common/common.module';
import { MedicalConditionModule } from 'src/medical-condition/medical-condition.module';
import { QuestionMedicalCondition } from './entities/question-medical-condition.entity';
import { UsersModule } from 'src/users/users.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Question, QuestionMedicalCondition]),
    CommonModule,
    MedicalConditionModule,
    UsersModule,
  ],
  controllers: [QuestionsController],
  providers: [QuestionsService],
  exports: [QuestionsService],
})
export class QuestionsModule {}
