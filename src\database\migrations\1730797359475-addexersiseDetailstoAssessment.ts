import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddexersiseDetailstoAssessment1730797359475
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'assessments',
      new TableColumn({
        name: 'word_tracing_exersice_score',
        type: 'decimal',
        precision: 10,
        scale: 2,
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      'assessments',
      new TableColumn({
        name: 'image_tracing_exersice_score',
        type: 'decimal',
        precision: 10,
        scale: 2,
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      'assessments',
      new TableColumn({
        name: 'questionare_pass',
        type: 'boolean',
        default: false,
      }),
    );

    await queryRunner.changeColumn(
      'assessments',
      'basic_questions_pass',
      new TableColumn({
        name: 'medical_questions_pass',
        type: 'boolean',
        default: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumn(
      'assessments',
      'medical_questions_pass',
      new TableColumn({
        name: 'basic_questions_pass',
        type: 'boolean',
        default: false,
      }),
    );
    await queryRunner.dropColumn('assessments', 'word_tracing_exersice_score');
    await queryRunner.dropColumn('assessments', 'image_tracing_exersice_score');
    await queryRunner.dropColumn('assessments', 'questionare_pass');
  }
}
