import {
  MigrationInterface,
  QueryRunner,
  Table<PERSON><PERSON>umn,
  TableForeign<PERSON>ey,
} from 'typeorm';

export class AddCOlumnsExersiseTypetoDownloadbalQuestionsAndType1732520841939
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'downloadable_questions',
      new TableColumn({
        name: 'exercise_type_id',
        type: 'varchar',
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      'downloadable_questions',
      new TableForeignKey({
        columnNames: ['exercise_type_id'],
        referencedTableName: 'exercise_types',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );

    await queryRunner.addColumn(
      'downloadable_questions_levels',
      new TableColumn({
        name: 'exercise_type_id',
        type: 'varchar',
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      'downloadable_questions_levels',
      new TableForeignKey({
        columnNames: ['exercise_type_id'],
        referencedTableName: 'exercise_types',
        referencedColumnNames: ['id'],
        onDelete: 'SET NULL',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const downloadQuestonsTable = await queryRunner.getTable(
      'downloadable_questions',
    );
    const foreignKey1 = downloadQuestonsTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('exercise_type_id') !== -1,
    );
    if (foreignKey1) {
      await queryRunner.dropForeignKey('downloadable_questions', foreignKey1);
    }
    await queryRunner.dropColumn('downloadable_questions', 'exercise_type_id');
    const downloadQuestionsLevelsTable = await queryRunner.getTable(
      'downloadable_questions_levels',
    );
    const foreignKey2 = downloadQuestionsLevelsTable.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('exercise_type_id') !== -1,
    );
    if (foreignKey2) {
      await queryRunner.dropForeignKey(
        'downloadable_questions_levels',
        foreignKey2,
      );
    }
    await queryRunner.dropColumn(
      'downloadable_questions_levels',
      'exercise_type_id',
    );
  }
}
