import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ExerciseType } from '../../exercise-types/entities/exercise-type.entity';
import { CustomGameAge } from './custom-game-age.entity';

@Entity('custom_games')
export class CustomGame {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => ExerciseType, (exerciseType) => exerciseType.customGames, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'exercise_type_id' }) // Explicitly specify the column name
  exerciseType: ExerciseType;

  @Column()
  name: string;

  @Column()
  exercise_type_id: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  thumbnail: string;

  @Column({
    type: 'enum',
    enum: ['stretching_game', 'tapping_game', 'puzzle_game', 'memory_game'],
    default: 'stretching_game',
  })
  type: string;

  @Column({ type: 'int', nullable: true })
  time_limit: number;

  @Column({ type: 'int', nullable: true })
  level: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => CustomGameAge, (CustomGameAge) => CustomGameAge.customGame, {
    cascade: true,
  })
  ages: CustomGameAge[];
}
