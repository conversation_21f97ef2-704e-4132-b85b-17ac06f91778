import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  NotFoundException,
} from '@nestjs/common';
import { TransationsService } from './transations.service';
import { CreateTransationDto } from './dto/create-transation.dto';
import { UpdateTransationDto } from './dto/update-transation.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Role } from 'src/common/decorators/roles.decorator';
import { UsersService } from 'src/users/users.service';
import { AssessmentsService } from 'src/assessments/assessments.service';
import { PlansService } from 'src/plans/plans.service';
import { StripeService } from 'src/stripe/stripe.service';
import { ResponseService } from 'src/common/services/response.service';

@Controller('transations')
export class TransationsController {
  constructor(
    private readonly transationsService: TransationsService,
    private readonly usersService: UsersService,
    private readonly assessmentService: AssessmentsService,
    private readonly planService: PlansService,
    private readonly stripeSercvice: StripeService,
    private readonly responseService: ResponseService,
  ) {}

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('teacher', 'parent')
  @Post()
  async create(
    @Body() createTransationDto: CreateTransationDto,
    @Request() req,
  ) {
    const user = await this.usersService.findOneByParam({ id: req.user.sub }, [
      'children',
    ]);
    const assessment = await this.assessmentService.findOne(
      createTransationDto.assessment_id,
    );
    if (!assessment) {
      throw new NotFoundException('Assessment Not Found');
    }
    const plan = await this.planService.findOne(createTransationDto.plan_id);
    if (!plan) {
      throw new NotFoundException('Plan Not Found');
    }
    //return this.transationsService.create(createTransationDto, user);
  }

  @Get()
  findAll() {
    return this.transationsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.transationsService.findOne(+id);
  }

  @Get('session-id/:id')
  async findOneBySessionID(@Param('id') id: string) {
    const transation = await this.transationsService.findOneByParam({
      payment_indent: id,
    });
    if (!transation) {
      throw new NotFoundException('Transation Not Found');
    }
    return this.responseService.successResponse('Transation Found', transation);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateTransationDto: UpdateTransationDto,
  ) {
    return this.transationsService.update(+id, updateTransationDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.transationsService.remove(+id);
  }
}
