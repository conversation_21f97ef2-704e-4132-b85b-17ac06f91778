import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  NotFoundException,
  ConflictException,
  UseGuards,
  Query,
} from '@nestjs/common';
import { QuestionsService } from './questions.service';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { ResponseService } from 'src/common/services/response.service';
import { MedicalConditionService } from 'src/medical-condition/medical-condition.service';
import { AddMedicalHistoryDto } from './dto/add-medical-history.dto';
import { AccessTokenGuard } from 'src/common/guards/accessToken.guard';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { Role } from 'src/common/decorators/roles.decorator';
import { filter } from 'rxjs';
import { FindManyOptions, Like, Raw } from 'typeorm';

@Controller('questions')
export class QuestionsController {
  constructor(
    private readonly questionsService: QuestionsService,
    private readonly responseService: ResponseService,
    private readonly medicalConditionService: MedicalConditionService,
  ) {}

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post()
  async create(@Body() createQuestionDto: CreateQuestionDto) {
    if (
      !createQuestionDto.options.some((option: string) =>
        createQuestionDto.triggering_answers.includes(option),
      )
    ) {
      throw new ConflictException(
        'Triggering answers must be a subset of options',
      );
    }
    const newQuestion = await this.questionsService.create(createQuestionDto);
    let questionMedicalConditons;
    if (createQuestionDto.medical_condition_ids) {
      questionMedicalConditons =
        await this.questionsService.addMedicalConditionsToQuestion(
          newQuestion,
          createQuestionDto.medical_condition_ids,
        );
    }
    newQuestion.questionMedicalConditions = questionMedicalConditons;
    return this.responseService.successResponse(
      'New Question Created Sucessfully',
      newQuestion,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Post(':id/add-medical-condition')
  async addMedicalHistory(
    @Param('id') questionId: string,
    @Body() addMedicalHistoryDto: AddMedicalHistoryDto,
  ) {
    const question = await this.questionsService.findOne(questionId);
    if (!question) {
      throw new NotFoundException('Question not found');
    }
    const data = await this.questionsService.addMedicalConditionsToQuestion(
      question,
      addMedicalHistoryDto.medical_condition_ids,
    );
    return this.responseService.successResponse(
      'Medical Conditions Added Sucessfully',
      data,
    );
  }

  @UseGuards(AccessTokenGuard)
  @Role('super_admin')
  @Get()
  async findAll(@Query() filter) {
    const questions = await this.questionsService.findAll();
    return this.responseService.successResponse('All Questions', questions);
  }

  @Get('paginated')
  async findAllPaginated(@Query() filter) {
    const page = parseInt(filter.page) || 1;
    const limit = parseInt(filter.limit) || 10;
    const skip = (page - 1) * limit;

    let where: FindManyOptions['where'] = {};

    if (filter.question_text) {
      where = { question_text: Like(`%${filter.question_text}%`) };
    }

    if (filter.question_type === 'medical_questions') {
      where = {
        ...where,
        rules: Raw(
          (alias) => `JSON_EXTRACT(${alias}, '$.medical_question') = true`,
        ),
      };
    } else if (filter.question_type === 'general_questions') {
      where = {
        ...where,
        rules: Raw(
          (alias) => `JSON_EXTRACT(${alias}, '$.medical_question') = false`,
        ),
      };
    }

    const [Questions, total] = await this.questionsService.findAndCountByParam(
      where,
      skip,
      limit,
    );

    return this.responseService.successResponse('Questions List', {
      questions: Questions,
      pagination_data: {
        currentPage: page,
        totalItems: total,
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  }

  @UseGuards(AccessTokenGuard)
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const question = await this.questionsService.findOne(id);
    return this.responseService.successResponse('Question Found', question);
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateQuestionDto: UpdateQuestionDto,
  ) {
    const updatedQuestion = await this.questionsService.update(
      id,
      updateQuestionDto,
    );
    let question;
    if (updateQuestionDto.medical_condition_ids) {
      question = await this.questionsService.addMedicalConditionsToQuestion(
        updatedQuestion,
        updateQuestionDto.medical_condition_ids,
      );
    }
    updatedQuestion.questionMedicalConditions =
      question.questionMedicalConditons;
    return this.responseService.successResponse(
      'Question Updated',
      updatedQuestion,
    );
  }

  @UseGuards(AccessTokenGuard, RolesGuard)
  @Role('super_admin')
  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.questionsService.remove(id);
    return this.responseService.successResponse('Question Removed');
  }
}
