import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>umn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';
import { HearAndSelect } from '../../hear-and-select/entities/hear-and-select.entity';

@Entity('hear_and_select_age')
export class HearAndSelectAge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => HearAndSelect, (hearAndSelect) => hearAndSelect.ages, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'hear_and_select_id' }) // Explicitly specify the column name
  hearAndSelect: HearAndSelect;

  @Column()
  age: number;

  @Column()
  hear_and_select_id: string;
}
