import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PlanPrice } from './entities/plan-price.entity';
import { PlanPricesService } from './plan-prices.service';
import { PlanPricesController } from './plan-prices.controller';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [TypeOrmModule.forFeature([PlanPrice]), CommonModule],
  controllers: [PlanPricesController],
  providers: [PlanPricesService],
  exports: [PlanPricesService], // Export TypeOrmModule for use in related modules
})
export class PlanPricesModule {}
