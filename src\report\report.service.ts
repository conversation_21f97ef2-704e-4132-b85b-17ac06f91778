import { BadRequestException, Injectable } from '@nestjs/common';
import fs from 'fs';
import * as PDFDocument from 'pdfkit';
import { createSummaryReportPDF } from './templates/basic-report-pdf-template';
import { createDetailedReportPDF } from './templates/detailed-report-pdf-template';

@Injectable()
export class ReportService {
  async generatePDFReport(filename: string, reportData: any): Promise<void> {
    try {
      const pdfBuffer = await createSummaryReportPDF(reportData);
      fs.writeFileSync(filename, pdfBuffer);
      console.log(`Report saved as ${filename}`);
    } catch (error) {
      console.error('Error generating PDF report:', error);
      throw new Error('Failed to generate PDF report');
    }
  }

  async generatePDFReportBuffer(
    reportData: any,
    report_type: 'summary' | 'detailed',
  ): Promise<Buffer> {
    if (report_type === 'summary') {
      return await createSummaryReportPDF(reportData);
    } else if (report_type === 'detailed') {
      return await createDetailedReportPDF(reportData);
    } else {
      throw new BadRequestException('Invalid report type');
    }
  }
  async generatePDF(): Promise<Buffer> {
    const pdfBuffer: Buffer = await new Promise((resolve) => {
      const reportData = {
        testDate: '15th Sept 2024',
        name: 'John Doe',
        dob: '1st Jan 2010',
        age: '14',
        gender: 'Male',
        schoolYear: '9th Grade',
        language: 'English',
        country: 'London, UK',
        tracingScore: 'Above average',
        imageTracingScore: 'Average',
        areasOfConcern: [
          'Motor dysgraphia',
          'Spatial dysgraphia',
          'Lexical dysgraphia',
          'Dyslexic dysgraphia',
          'Phonological dysgraphia',
          'Attention Deficit',
        ],
      };
      const doc = new PDFDocument({
        size: 'LETTER',
        bufferPages: true,
      });
      doc.text('hello world', 100, 50);
      doc.end();

      const buffer = [];
      doc.on('data', buffer.push.bind(buffer));
      doc.on('end', () => {
        const data = Buffer.concat(buffer);
        resolve(data);
      });
    });

    return pdfBuffer;
  }
}
